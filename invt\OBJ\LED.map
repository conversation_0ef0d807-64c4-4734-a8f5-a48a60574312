Component: ARM Compiler 5.06 update 6 (build 750) Tool: armlink [4d35ed]

==============================================================================

Section Cross References

    main.o(i.DMA2_Stream0_IRQHandler) refers to stm32f4xx_dma.o(i.DMA_GetITStatus) for DMA_GetITStatus
    main.o(i.DMA2_Stream0_IRQHandler) refers to stm32f4xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    main.o(i.DMA2_Stream0_IRQHandler) refers to inveter.o(i.invt_loop) for invt_loop
    main.o(i.DMA2_Stream0_IRQHandler) refers to stm32f4xx_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    main.o(i.DMA2_Stream0_IRQHandler) refers to stm32f4xx_dma.o(i.DMA_ClearITPendingBit) for DMA_ClearITPendingBit
    main.o(i.DMA2_Stream0_IRQHandler) refers to ad.o(.data) for ADC_Value
    main.o(i.DMA2_Stream0_IRQHandler) refers to main.o(.bss) for invt
    main.o(i.DMA2_Stream0_IRQHandler) refers to main.o(.data) for flag2
    main.o(i.DMA2_Stream0_IRQHandler) refers to sine.o(.bss) for sinData
    main.o(i.main) refers to inveter.o(i.invt_init) for invt_init
    main.o(i.main) refers to sine.o(i.get_sin_tab1) for get_sin_tab1
    main.o(i.main) refers to pfc.o(i.pfc_init) for pfc_init
    main.o(i.main) refers to timer1.o(i.TIM8_PWM_CHN_Init) for TIM8_PWM_CHN_Init
    main.o(i.main) refers to timer1.o(i.TIM1_PWM_CHN_Init) for TIM1_PWM_CHN_Init
    main.o(i.main) refers to key.o(i.KEY_Init) for KEY_Init
    main.o(i.main) refers to led.o(i.LED_Init) for LED_Init
    main.o(i.main) refers to delay.o(i.delay_init) for delay_init
    main.o(i.main) refers to oled.o(i.OLED_Init) for OLED_Init
    main.o(i.main) refers to usart.o(i.uart_init) for uart_init
    main.o(i.main) refers to ad.o(i.MyTIM3_Init) for MyTIM3_Init
    main.o(i.main) refers to ad.o(i.MyADC1_Init) for MyADC1_Init
    main.o(i.main) refers to ad.o(i.MyDMA1_Init) for MyDMA1_Init
    main.o(i.main) refers to stm32f4xx_tim.o(i.TIM_Cmd) for TIM_Cmd
    main.o(i.main) refers to misc.o(i.NVIC_PriorityGroupConfig) for NVIC_PriorityGroupConfig
    main.o(i.main) refers to key.o(i.KEY_Scan3) for KEY_Scan3
    main.o(i.main) refers to stm32f4xx_tim.o(i.TIM_ARRPreloadConfig) for TIM_ARRPreloadConfig
    main.o(i.main) refers to stm32f4xx_tim.o(i.TIM_CtrlPWMOutputs) for TIM_CtrlPWMOutputs
    main.o(i.main) refers to key.o(i.KEY_Calculate) for KEY_Calculate
    main.o(i.main) refers to main.o(.bss) for invt
    main.o(i.main) refers to main.o(.data) for switch_
    system_stm32f4xx.o(i.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    system_stm32f4xx.o(i.SystemInit) refers to system_stm32f4xx.o(i.SetSysClock) for SetSysClock
    led.o(i.LED_Init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    led.o(i.LED_Init) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    led.o(i.LED_Init) refers to stm32f4xx_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    sine.o(i.get_sin_tab1) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    sine.o(i.get_sin_tab1) refers to sin.o(i.__hardfp_sin) for __hardfp_sin
    sine.o(i.get_sin_tab1) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    sine.o(i.get_sin_tab1) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    sine.o(i.get_sin_tab1) refers to sine.o(.bss) for sinData
    vofa.o(i.Float_to_byte) refers to vofa.o(.data) for vofa
    vofa.o(i.send_to_vofa1) refers to stm32f4xx_usart.o(i.USART_SendData) for USART_SendData
    vofa.o(i.send_to_vofa1) refers to stm32f4xx_usart.o(i.USART_GetFlagStatus) for USART_GetFlagStatus
    vofa.o(i.send_to_vofa1) refers to vofa.o(.data) for tail
    vofa.o(i.send_to_vofa2) refers to vofa.o(i.Float_to_byte) for Float_to_byte
    vofa.o(i.send_to_vofa2) refers to stm32f4xx_usart.o(i.USART_SendData) for USART_SendData
    vofa.o(i.send_to_vofa2) refers to stm32f4xx_usart.o(i.USART_GetFlagStatus) for USART_GetFlagStatus
    vofa.o(i.send_to_vofa2) refers to vofa.o(.data) for tail
    ad.o(i.MyADC1_Init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    ad.o(i.MyADC1_Init) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    ad.o(i.MyADC1_Init) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    ad.o(i.MyADC1_Init) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    ad.o(i.MyADC1_Init) refers to stm32f4xx_adc.o(i.ADC_CommonInit) for ADC_CommonInit
    ad.o(i.MyADC1_Init) refers to stm32f4xx_adc.o(i.ADC_Init) for ADC_Init
    ad.o(i.MyADC1_Init) refers to stm32f4xx_adc.o(i.ADC_RegularChannelConfig) for ADC_RegularChannelConfig
    ad.o(i.MyADC1_Init) refers to stm32f4xx_adc.o(i.ADC_Cmd) for ADC_Cmd
    ad.o(i.MyDMA1_Init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    ad.o(i.MyDMA1_Init) refers to stm32f4xx_dma.o(i.DMA_Init) for DMA_Init
    ad.o(i.MyDMA1_Init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    ad.o(i.MyDMA1_Init) refers to misc.o(i.NVIC_PriorityGroupConfig) for NVIC_PriorityGroupConfig
    ad.o(i.MyDMA1_Init) refers to stm32f4xx_dma.o(i.DMA_ITConfig) for DMA_ITConfig
    ad.o(i.MyDMA1_Init) refers to stm32f4xx_adc.o(i.ADC_DMARequestAfterLastTransferCmd) for ADC_DMARequestAfterLastTransferCmd
    ad.o(i.MyDMA1_Init) refers to stm32f4xx_adc.o(i.ADC_DMACmd) for ADC_DMACmd
    ad.o(i.MyDMA1_Init) refers to stm32f4xx_dma.o(i.DMA_Cmd) for DMA_Cmd
    ad.o(i.MyDMA1_Init) refers to ad.o(.data) for ADC_Value
    ad.o(i.MyGPIO_Init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    ad.o(i.MyGPIO_Init) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    ad.o(i.MyTIM3_Init) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    ad.o(i.MyTIM3_Init) refers to stm32f4xx_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    ad.o(i.MyTIM3_Init) refers to stm32f4xx_tim.o(i.TIM_SelectOutputTrigger) for TIM_SelectOutputTrigger
    ad.o(i.MyTIM3_Init) refers to stm32f4xx_tim.o(i.TIM_Cmd) for TIM_Cmd
    oled.o(i.OLED_Clear) refers to oled.o(i.OLED_SetCursor) for OLED_SetCursor
    oled.o(i.OLED_Clear) refers to oled.o(i.OLED_WriteData) for OLED_WriteData
    oled.o(i.OLED_Float) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    oled.o(i.OLED_Float) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    oled.o(i.OLED_Float) refers to dfix.o(x$fpl$dfix) for __aeabi_d2iz
    oled.o(i.OLED_Float) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    oled.o(i.OLED_Float) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    oled.o(i.OLED_Float) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    oled.o(i.OLED_Float) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    oled.o(i.OLED_Float) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    oled.o(i.OLED_I2C_Init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    oled.o(i.OLED_I2C_Init) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    oled.o(i.OLED_I2C_Init) refers to stm32f4xx_gpio.o(i.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(i.OLED_I2C_Init) refers to delay.o(i.delay_us) for delay_us
    oled.o(i.OLED_I2C_SendByte) refers to stm32f4xx_gpio.o(i.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(i.OLED_I2C_SendByte) refers to delay.o(i.delay_us) for delay_us
    oled.o(i.OLED_I2C_Start) refers to stm32f4xx_gpio.o(i.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(i.OLED_I2C_Start) refers to delay.o(i.delay_us) for delay_us
    oled.o(i.OLED_I2C_Stop) refers to stm32f4xx_gpio.o(i.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(i.OLED_I2C_Stop) refers to delay.o(i.delay_us) for delay_us
    oled.o(i.OLED_Init) refers to delay.o(i.delay_ms) for delay_ms
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_I2C_Init) for OLED_I2C_Init
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_WriteCommand) for OLED_WriteCommand
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_Clear) for OLED_Clear
    oled.o(i.OLED_SetCursor) refers to oled.o(i.OLED_WriteCommand) for OLED_WriteCommand
    oled.o(i.OLED_ShowBinNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowBinNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowChar) refers to oled.o(i.OLED_SetCursor) for OLED_SetCursor
    oled.o(i.OLED_ShowChar) refers to oled.o(i.OLED_WriteData) for OLED_WriteData
    oled.o(i.OLED_ShowChar) refers to oled.o(.constdata) for OLED_F8x16
    oled.o(i.OLED_ShowHexNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowHexNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowSignedNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowSignedNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowString) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_WriteCommand) refers to oled.o(i.OLED_I2C_Start) for OLED_I2C_Start
    oled.o(i.OLED_WriteCommand) refers to oled.o(i.OLED_I2C_SendByte) for OLED_I2C_SendByte
    oled.o(i.OLED_WriteCommand) refers to oled.o(i.OLED_I2C_Stop) for OLED_I2C_Stop
    oled.o(i.OLED_WriteData) refers to oled.o(i.OLED_I2C_Start) for OLED_I2C_Start
    oled.o(i.OLED_WriteData) refers to oled.o(i.OLED_I2C_SendByte) for OLED_I2C_SendByte
    oled.o(i.OLED_WriteData) refers to oled.o(i.OLED_I2C_Stop) for OLED_I2C_Stop
    pfc.o(i.pfc_loop) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    pfc.o(i.pfc_loop) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    pfc.o(i.pfc_loop) refers to arm_cos_f32.o(.text) for arm_cos_f32
    pfc.o(i.pfc_loop) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    pfc.o(i.pfc_loop) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    pfc.o(i.pfc_loop) refers to pfc.o(.data) for pf
    pfc.o(i.sogi_pll) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    pfc.o(i.sogi_pll) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    pfc.o(i.sogi_pll) refers to arm_sin_f32.o(.text) for arm_sin_f32
    pfc.o(i.sogi_pll) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    pfc.o(i.sogi_pll) refers to arm_cos_f32.o(.text) for arm_cos_f32
    pfc.o(i.sogi_pll) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    pfc.o(i.sogi_pll) refers to pfc.o(i.wd_limit) for wd_limit
    timer1.o(i.TIM1_PWM_CHN_Init) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    timer1.o(i.TIM1_PWM_CHN_Init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    timer1.o(i.TIM1_PWM_CHN_Init) refers to stm32f4xx_gpio.o(i.GPIO_PinAFConfig) for GPIO_PinAFConfig
    timer1.o(i.TIM1_PWM_CHN_Init) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    timer1.o(i.TIM1_PWM_CHN_Init) refers to stm32f4xx_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    timer1.o(i.TIM1_PWM_CHN_Init) refers to stm32f4xx_tim.o(i.TIM_BDTRConfig) for TIM_BDTRConfig
    timer1.o(i.TIM1_PWM_CHN_Init) refers to stm32f4xx_tim.o(i.TIM_OC1Init) for TIM_OC1Init
    timer1.o(i.TIM1_PWM_CHN_Init) refers to stm32f4xx_tim.o(i.TIM_OC2Init) for TIM_OC2Init
    timer1.o(i.TIM1_PWM_CHN_Init) refers to stm32f4xx_tim.o(i.TIM_OC3Init) for TIM_OC3Init
    timer1.o(i.TIM1_PWM_CHN_Init) refers to stm32f4xx_tim.o(i.TIM_OC1PreloadConfig) for TIM_OC1PreloadConfig
    timer1.o(i.TIM1_PWM_CHN_Init) refers to stm32f4xx_tim.o(i.TIM_OC2PreloadConfig) for TIM_OC2PreloadConfig
    timer1.o(i.TIM1_PWM_CHN_Init) refers to stm32f4xx_tim.o(i.TIM_OC3PreloadConfig) for TIM_OC3PreloadConfig
    timer1.o(i.TIM1_PWM_CHN_Init) refers to stm32f4xx_tim.o(i.TIM_ARRPreloadConfig) for TIM_ARRPreloadConfig
    timer1.o(i.TIM1_PWM_CHN_Init) refers to stm32f4xx_tim.o(i.TIM_Cmd) for TIM_Cmd
    timer1.o(i.TIM1_PWM_CHN_Init) refers to stm32f4xx_tim.o(i.TIM_CtrlPWMOutputs) for TIM_CtrlPWMOutputs
    timer1.o(i.TIM8_PWM_CHN_Init) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    timer1.o(i.TIM8_PWM_CHN_Init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    timer1.o(i.TIM8_PWM_CHN_Init) refers to stm32f4xx_gpio.o(i.GPIO_PinAFConfig) for GPIO_PinAFConfig
    timer1.o(i.TIM8_PWM_CHN_Init) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    timer1.o(i.TIM8_PWM_CHN_Init) refers to stm32f4xx_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    timer1.o(i.TIM8_PWM_CHN_Init) refers to stm32f4xx_tim.o(i.TIM_BDTRConfig) for TIM_BDTRConfig
    timer1.o(i.TIM8_PWM_CHN_Init) refers to stm32f4xx_tim.o(i.TIM_OC1Init) for TIM_OC1Init
    timer1.o(i.TIM8_PWM_CHN_Init) refers to stm32f4xx_tim.o(i.TIM_OC2Init) for TIM_OC2Init
    timer1.o(i.TIM8_PWM_CHN_Init) refers to stm32f4xx_tim.o(i.TIM_OC3Init) for TIM_OC3Init
    timer1.o(i.TIM8_PWM_CHN_Init) refers to stm32f4xx_tim.o(i.TIM_OC1PreloadConfig) for TIM_OC1PreloadConfig
    timer1.o(i.TIM8_PWM_CHN_Init) refers to stm32f4xx_tim.o(i.TIM_OC2PreloadConfig) for TIM_OC2PreloadConfig
    timer1.o(i.TIM8_PWM_CHN_Init) refers to stm32f4xx_tim.o(i.TIM_OC3PreloadConfig) for TIM_OC3PreloadConfig
    timer1.o(i.TIM8_PWM_CHN_Init) refers to stm32f4xx_tim.o(i.TIM_ARRPreloadConfig) for TIM_ARRPreloadConfig
    timer1.o(i.TIM8_PWM_CHN_Init) refers to stm32f4xx_tim.o(i.TIM_Cmd) for TIM_Cmd
    timer1.o(i.TIM8_PWM_CHN_Init) refers to stm32f4xx_tim.o(i.TIM_CtrlPWMOutputs) for TIM_CtrlPWMOutputs
    show.o(i.ARR_adjust) refers to key.o(i.KEY_Scan1) for KEY_Scan1
    show.o(i.ARR_adjust) refers to show.o(i.update_TIM_ARR) for update_TIM_ARR
    show.o(i.ARR_adjust) refers to show.o(.constdata) for ARR_Table
    show.o(i.pf_adjust) refers to key.o(i.KEY_Scan1) for KEY_Scan1
    show.o(i.show2) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    show.o(i.show2) refers to oled.o(i.OLED_Float) for OLED_Float
    show.o(i.show2) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    show.o(i.show_AD) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    show.o(i.show_memu) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    show.o(i.show_udc) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    show.o(i.show_udc) refers to oled.o(i.OLED_Float) for OLED_Float
    show.o(i.update_TIM_ARR) refers to stm32f4xx_tim.o(i.TIM_Cmd) for TIM_Cmd
    show.o(i.update_TIM_ARR) refers to stm32f4xx_tim.o(i.TIM_SetAutoreload) for TIM_SetAutoreload
    show.o(i.update_TIM_ARR) refers to stm32f4xx_tim.o(i.TIM_GenerateEvent) for TIM_GenerateEvent
    show.o(i.vref_adjust) refers to key.o(i.KEY_Scan1) for KEY_Scan1
    key.o(i.KEY_Calculate) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    key.o(i.KEY_Calculate) refers to stm32f4xx_gpio.o(i.GPIO_ReadInputDataBit) for GPIO_ReadInputDataBit
    key.o(i.KEY_Calculate) refers to oled.o(i.OLED_Clear) for OLED_Clear
    key.o(i.KEY_Calculate) refers to show.o(i.show2) for show2
    key.o(i.KEY_Calculate) refers to show.o(i.ARR_adjust) for ARR_adjust
    key.o(i.KEY_Calculate) refers to show.o(i.show_AD) for show_AD
    key.o(i.KEY_Calculate) refers to key.o(.data) for key_up
    key.o(i.KEY_Calculate) refers to main.o(.bss) for invt
    key.o(i.KEY_Init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    key.o(i.KEY_Init) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    key.o(i.KEY_Scan1) refers to stm32f4xx_gpio.o(i.GPIO_ReadInputDataBit) for GPIO_ReadInputDataBit
    key.o(i.KEY_Scan1) refers to key.o(.data) for key_up
    key.o(i.KEY_Scan3) refers to stm32f4xx_gpio.o(i.GPIO_ReadInputDataBit) for GPIO_ReadInputDataBit
    key.o(i.KEY_Scan3) refers to key.o(.data) for key_up
    delay.o(i.delay_init) refers to misc.o(i.SysTick_CLKSourceConfig) for SysTick_CLKSourceConfig
    delay.o(i.delay_init) refers to delay.o(.data) for fac_us
    delay.o(i.delay_ms) refers to delay.o(i.delay_xms) for delay_xms
    delay.o(i.delay_us) refers to delay.o(.data) for fac_us
    delay.o(i.delay_xms) refers to delay.o(.data) for fac_ms
    usart.o(.rev16_text) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(.revsh_text) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.USART1_IRQHandler) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.USART1_IRQHandler) refers to stm32f4xx_usart.o(i.USART_GetITStatus) for USART_GetITStatus
    usart.o(i.USART1_IRQHandler) refers to stm32f4xx_usart.o(i.USART_ReceiveData) for USART_ReceiveData
    usart.o(i.USART1_IRQHandler) refers to usart.o(.data) for USART_RX_STA
    usart.o(i.USART1_IRQHandler) refers to usart.o(.bss) for USART_RX_BUF
    usart.o(i._sys_exit) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.fputc) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.uart_init) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.uart_init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    usart.o(i.uart_init) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    usart.o(i.uart_init) refers to stm32f4xx_gpio.o(i.GPIO_PinAFConfig) for GPIO_PinAFConfig
    usart.o(i.uart_init) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    usart.o(i.uart_init) refers to stm32f4xx_usart.o(i.USART_Init) for USART_Init
    usart.o(i.uart_init) refers to stm32f4xx_usart.o(i.USART_Cmd) for USART_Cmd
    usart.o(i.uart_init) refers to stm32f4xx_usart.o(i.USART_ITConfig) for USART_ITConfig
    usart.o(i.uart_init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    usart.o(.bss) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(.data) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    startup_stm32f40_41xxx.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f40_41xxx.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f40_41xxx.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f40_41xxx.o(RESET) refers to startup_stm32f40_41xxx.o(STACK) for __initial_sp
    startup_stm32f40_41xxx.o(RESET) refers to startup_stm32f40_41xxx.o(.text) for Reset_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f40_41xxx.o(RESET) refers to usart.o(i.USART1_IRQHandler) for USART1_IRQHandler
    startup_stm32f40_41xxx.o(RESET) refers to main.o(i.DMA2_Stream0_IRQHandler) for DMA2_Stream0_IRQHandler
    startup_stm32f40_41xxx.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f40_41xxx.o(.text) refers to system_stm32f4xx.o(i.SystemInit) for SystemInit
    startup_stm32f40_41xxx.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32f40_41xxx.o(.text) refers to startup_stm32f40_41xxx.o(HEAP) for Heap_Mem
    startup_stm32f40_41xxx.o(.text) refers to startup_stm32f40_41xxx.o(STACK) for Stack_Mem
    stm32f4xx_gpio.o(i.GPIO_DeInit) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphResetCmd) for RCC_AHB1PeriphResetCmd
    stm32f4xx_rcc.o(i.RCC_GetClocksFreq) refers to stm32f4xx_rcc.o(.data) for APBAHBPrescTable
    stm32f4xx_rcc.o(i.RCC_WaitForHSEStartUp) refers to stm32f4xx_rcc.o(i.RCC_GetFlagStatus) for RCC_GetFlagStatus
    stm32f4xx_syscfg.o(i.SYSCFG_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f4xx_usart.o(i.USART_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f4xx_usart.o(i.USART_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f4xx_usart.o(i.USART_Init) refers to stm32f4xx_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f4xx_adc.o(i.ADC_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f4xx_tim.o(i.TIM_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f4xx_tim.o(i.TIM_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f4xx_tim.o(i.TIM_ETRClockMode1Config) refers to stm32f4xx_tim.o(i.TIM_ETRConfig) for TIM_ETRConfig
    stm32f4xx_tim.o(i.TIM_ETRClockMode2Config) refers to stm32f4xx_tim.o(i.TIM_ETRConfig) for TIM_ETRConfig
    stm32f4xx_tim.o(i.TIM_ICInit) refers to stm32f4xx_tim.o(i.TI1_Config) for TI1_Config
    stm32f4xx_tim.o(i.TIM_ICInit) refers to stm32f4xx_tim.o(i.TIM_SetIC1Prescaler) for TIM_SetIC1Prescaler
    stm32f4xx_tim.o(i.TIM_ICInit) refers to stm32f4xx_tim.o(i.TI2_Config) for TI2_Config
    stm32f4xx_tim.o(i.TIM_ICInit) refers to stm32f4xx_tim.o(i.TIM_SetIC2Prescaler) for TIM_SetIC2Prescaler
    stm32f4xx_tim.o(i.TIM_ICInit) refers to stm32f4xx_tim.o(i.TI3_Config) for TI3_Config
    stm32f4xx_tim.o(i.TIM_ICInit) refers to stm32f4xx_tim.o(i.TIM_SetIC3Prescaler) for TIM_SetIC3Prescaler
    stm32f4xx_tim.o(i.TIM_ICInit) refers to stm32f4xx_tim.o(i.TI4_Config) for TI4_Config
    stm32f4xx_tim.o(i.TIM_ICInit) refers to stm32f4xx_tim.o(i.TIM_SetIC4Prescaler) for TIM_SetIC4Prescaler
    stm32f4xx_tim.o(i.TIM_ITRxExternalClockConfig) refers to stm32f4xx_tim.o(i.TIM_SelectInputTrigger) for TIM_SelectInputTrigger
    stm32f4xx_tim.o(i.TIM_PWMIConfig) refers to stm32f4xx_tim.o(i.TI1_Config) for TI1_Config
    stm32f4xx_tim.o(i.TIM_PWMIConfig) refers to stm32f4xx_tim.o(i.TIM_SetIC1Prescaler) for TIM_SetIC1Prescaler
    stm32f4xx_tim.o(i.TIM_PWMIConfig) refers to stm32f4xx_tim.o(i.TI2_Config) for TI2_Config
    stm32f4xx_tim.o(i.TIM_PWMIConfig) refers to stm32f4xx_tim.o(i.TIM_SetIC2Prescaler) for TIM_SetIC2Prescaler
    stm32f4xx_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f4xx_tim.o(i.TI2_Config) for TI2_Config
    stm32f4xx_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f4xx_tim.o(i.TI1_Config) for TI1_Config
    stm32f4xx_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f4xx_tim.o(i.TIM_SelectInputTrigger) for TIM_SelectInputTrigger
    stm32f4xx_dma2d.o(i.DMA2D_DeInit) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphResetCmd) for RCC_AHB1PeriphResetCmd
    arm_cos_f32.o(.text) refers to arm_cos_f32.o(.constdata) for .constdata
    arm_sin_f32.o(.text) refers to arm_sin_f32.o(.constdata) for .constdata
    use_no_semi_2.o(.text) refers (Special) to use_no_semi.o(.text) for __use_no_semihosting_swi
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    basic.o(x$fpl$basic) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    d2f.o(x$fpl$d2f) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    d2f.o(x$fpl$d2f) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    d2f.o(x$fpl$d2f) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    daddsub_clz.o(x$fpl$dadd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dadd) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dadd) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    daddsub_clz.o(x$fpl$dadd) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    daddsub_clz.o(x$fpl$drsb) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dsub) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dsub) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$dsub) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfix.o(x$fpl$dfix) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfix.o(x$fpl$dfix) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfix.o(x$fpl$dfixr) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfix.o(x$fpl$dfixr) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dflt_clz.o(x$fpl$dfltu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dflt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dfltn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dleqf.o(x$fpl$dleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dleqf.o(x$fpl$dleqf) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dleqf.o(x$fpl$dleqf) refers to dcmpi.o(x$fpl$dcmpinf) for __fpl_dcmp_Inf
    dmul.o(x$fpl$dmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(x$fpl$dmul) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    dmul.o(x$fpl$dmul) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    f2d.o(x$fpl$f2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    f2d.o(x$fpl$f2d) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    f2d.o(x$fpl$f2d) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    sin.o(i.__hardfp_sin) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin.o(i.__hardfp_sin) refers to _rserrno.o(.text) for __set_errno
    sin.o(i.__hardfp_sin) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    sin.o(i.__hardfp_sin) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    sin.o(i.__hardfp_sin) refers to rred.o(i.__ieee754_rem_pio2) for __ieee754_rem_pio2
    sin.o(i.__hardfp_sin) refers to cos_i.o(i.__kernel_cos) for __kernel_cos
    sin.o(i.__hardfp_sin) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    sin.o(i.__hardfp_sin) refers to sin_i.o(i.__kernel_sin) for __kernel_sin
    sin.o(i.__softfp_sin) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin.o(i.__softfp_sin) refers to sin.o(i.__hardfp_sin) for __hardfp_sin
    sin.o(i.sin) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin.o(i.sin) refers to sin.o(i.__hardfp_sin) for __hardfp_sin
    sin_x.o(i.____hardfp_sin$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin_x.o(i.____hardfp_sin$lsc) refers to _rserrno.o(.text) for __set_errno
    sin_x.o(i.____hardfp_sin$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    sin_x.o(i.____hardfp_sin$lsc) refers to rred.o(i.__ieee754_rem_pio2) for __ieee754_rem_pio2
    sin_x.o(i.____hardfp_sin$lsc) refers to cos_i.o(i.__kernel_cos) for __kernel_cos
    sin_x.o(i.____hardfp_sin$lsc) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    sin_x.o(i.____hardfp_sin$lsc) refers to sin_i.o(i.__kernel_sin) for __kernel_sin
    sin_x.o(i.____softfp_sin$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin_x.o(i.____softfp_sin$lsc) refers to sin_x.o(i.____hardfp_sin$lsc) for ____hardfp_sin$lsc
    sin_x.o(i.__sin$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin_x.o(i.__sin$lsc) refers to sin_x.o(i.____hardfp_sin$lsc) for ____hardfp_sin$lsc
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    _rserrno.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    dcmpi.o(x$fpl$dcmpinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dnaninf.o(x$fpl$dnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dretinf.o(x$fpl$dretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fnaninf.o(x$fpl$fnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fretinf.o(x$fpl$fretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cos_i.o(i.__kernel_cos) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cos_i.o(i.__kernel_cos) refers to dfix.o(x$fpl$dfix) for __aeabi_d2iz
    cos_i.o(i.__kernel_cos) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    cos_i.o(i.__kernel_cos) refers to poly.o(i.__kernel_poly) for __kernel_poly
    cos_i.o(i.__kernel_cos) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    cos_i.o(i.__kernel_cos) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    cos_i.o(i.__kernel_cos) refers to cos_i.o(.constdata) for .constdata
    cos_i.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dunder.o(i.__mathlib_dbl_divzero) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_infnan) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_infnan2) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_invalid) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_overflow) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_posinfnan) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_underflow) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    rred.o(i.__ieee754_rem_pio2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    rred.o(i.__ieee754_rem_pio2) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    rred.o(i.__ieee754_rem_pio2) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    rred.o(i.__ieee754_rem_pio2) refers to fabs.o(i.fabs) for fabs
    rred.o(i.__ieee754_rem_pio2) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    rred.o(i.__ieee754_rem_pio2) refers to dfix.o(x$fpl$dfix) for __aeabi_d2iz
    rred.o(i.__ieee754_rem_pio2) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    rred.o(i.__ieee754_rem_pio2) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    rred.o(i.__ieee754_rem_pio2) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    rred.o(i.__ieee754_rem_pio2) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    rred.o(i.__ieee754_rem_pio2) refers to rred.o(.constdata) for .constdata
    rred.o(i.__use_accurate_range_reduction) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    rred.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin_i.o(i.__kernel_sin) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin_i.o(i.__kernel_sin) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    sin_i.o(i.__kernel_sin) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    sin_i.o(i.__kernel_sin) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    sin_i.o(i.__kernel_sin) refers to poly.o(i.__kernel_poly) for __kernel_poly
    sin_i.o(i.__kernel_sin) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    sin_i.o(i.__kernel_sin) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    sin_i.o(i.__kernel_sin) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    sin_i.o(i.__kernel_sin) refers to sin_i.o(.constdata) for .constdata
    sin_i.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin_i_x.o(i.____kernel_sin$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin_i_x.o(i.____kernel_sin$lsc) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    sin_i_x.o(i.____kernel_sin$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    sin_i_x.o(i.____kernel_sin$lsc) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    sin_i_x.o(i.____kernel_sin$lsc) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    sin_i_x.o(i.____kernel_sin$lsc) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    sin_i_x.o(i.____kernel_sin$lsc) refers to sin_i_x.o(.constdata) for .constdata
    sin_i_x.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(i.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    rt_errno_addr.o(.text) refers to rt_errno_addr.o(.bss) for __aeabi_errno_addr_data
    rt_errno_addr_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    ddiv.o(x$fpl$drdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$drdiv) refers to ddiv.o(x$fpl$ddiv) for ddiv_entry
    ddiv.o(x$fpl$ddiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$ddiv) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    ddiv.o(x$fpl$ddiv) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    fabs.o(i.__hardfp_fabs) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fabs.o(i.__softfp_fabs) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fabs.o(i.fabs) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    poly.o(i.__kernel_poly) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    poly.o(i.__kernel_poly) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    poly.o(i.__kernel_poly) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32f40_41xxx.o(.text) for __user_initial_stackheap
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000001) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    libinit2.o(.ARM.Collect$$libinit$$00000001) refers to fpinit.o(x$fpl$fpinit) for _fp_init
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to usart.o(i._sys_exit) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000007) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000010) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_user_alloc_1
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to usart.o(i._sys_exit) for _sys_exit
    defsig_exit.o(.text) refers to usart.o(i._sys_exit) for _sys_exit
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_it.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_it.o(.revsh_text), (4 bytes).
    Removing system_stm32f4xx.o(.rev16_text), (4 bytes).
    Removing system_stm32f4xx.o(.revsh_text), (4 bytes).
    Removing system_stm32f4xx.o(i.SystemCoreClockUpdate), (192 bytes).
    Removing system_stm32f4xx.o(.data), (20 bytes).
    Removing led.o(.rev16_text), (4 bytes).
    Removing led.o(.revsh_text), (4 bytes).
    Removing sine.o(.rev16_text), (4 bytes).
    Removing sine.o(.revsh_text), (4 bytes).
    Removing vofa.o(.rev16_text), (4 bytes).
    Removing vofa.o(.revsh_text), (4 bytes).
    Removing vofa.o(i.Float_to_byte), (40 bytes).
    Removing vofa.o(i.send_to_vofa1), (84 bytes).
    Removing vofa.o(i.send_to_vofa2), (124 bytes).
    Removing vofa.o(.data), (8 bytes).
    Removing ad.o(.rev16_text), (4 bytes).
    Removing ad.o(.revsh_text), (4 bytes).
    Removing ad.o(i.MyGPIO_Init), (40 bytes).
    Removing oled.o(.rev16_text), (4 bytes).
    Removing oled.o(.revsh_text), (4 bytes).
    Removing oled.o(i.OLED_Pow), (20 bytes).
    Removing oled.o(i.OLED_ShowBinNum), (62 bytes).
    Removing oled.o(i.OLED_ShowHexNum), (84 bytes).
    Removing oled.o(i.OLED_ShowNum), (68 bytes).
    Removing oled.o(i.OLED_ShowSignedNum), (102 bytes).
    Removing pfc.o(.rev16_text), (4 bytes).
    Removing pfc.o(.revsh_text), (4 bytes).
    Removing pfc.o(i.pfc_loop), (748 bytes).
    Removing pfc.o(i.sogi_pll), (488 bytes).
    Removing pfc.o(i.wd_limit), (56 bytes).
    Removing pfc.o(.data), (408 bytes).
    Removing timer1.o(.rev16_text), (4 bytes).
    Removing timer1.o(.revsh_text), (4 bytes).
    Removing show.o(.rev16_text), (4 bytes).
    Removing show.o(.revsh_text), (4 bytes).
    Removing show.o(i.pf_adjust), (42 bytes).
    Removing show.o(i.show_memu), (84 bytes).
    Removing show.o(i.show_udc), (176 bytes).
    Removing show.o(i.vref_adjust), (62 bytes).
    Removing key.o(.rev16_text), (4 bytes).
    Removing key.o(.revsh_text), (4 bytes).
    Removing inveter.o(.rev16_text), (4 bytes).
    Removing inveter.o(.revsh_text), (4 bytes).
    Removing inveter.o(.data), (8 bytes).
    Removing delay.o(.rev16_text), (4 bytes).
    Removing delay.o(.revsh_text), (4 bytes).
    Removing sys.o(.rev16_text), (4 bytes).
    Removing sys.o(.revsh_text), (4 bytes).
    Removing sys.o(.emb_text), (16 bytes).
    Removing usart.o(.rev16_text), (4 bytes).
    Removing usart.o(.revsh_text), (4 bytes).
    Removing usart.o(i.fputc), (28 bytes).
    Removing misc.o(.rev16_text), (4 bytes).
    Removing misc.o(.revsh_text), (4 bytes).
    Removing misc.o(i.NVIC_SetVectorTable), (20 bytes).
    Removing misc.o(i.NVIC_SystemLPConfig), (32 bytes).
    Removing stm32f4xx_gpio.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_gpio.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_DeInit), (312 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_PinLockConfig), (34 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_ReadInputData), (8 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_ReadOutputData), (8 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_ReadOutputDataBit), (18 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_StructInit), (18 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_ToggleBits), (8 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_Write), (4 bytes).
    Removing stm32f4xx_rcc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_rcc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockLPModeCmd), (32 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB1PeriphResetCmd), (32 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB2PeriphClockCmd), (32 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB2PeriphClockLPModeCmd), (32 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB2PeriphResetCmd), (32 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB3PeriphClockCmd), (32 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB3PeriphClockLPModeCmd), (32 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB3PeriphResetCmd), (32 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_APB1PeriphClockLPModeCmd), (32 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd), (32 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_APB2PeriphClockLPModeCmd), (32 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AdjustHSICalibrationValue), (24 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_BackupResetCmd), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_ClearFlag), (20 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_ClearITPendingBit), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_ClockSecuritySystemCmd), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_DeInit), (100 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_GetFlagStatus), (64 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_GetITStatus), (24 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_GetSYSCLKSource), (16 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_HCLKConfig), (24 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_HSEConfig), (16 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_HSICmd), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_I2SCLKConfig), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_ITConfig), (32 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_LSEConfig), (44 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_LSEModeConfig), (36 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_LSICmd), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_LTDCCLKDivConfig), (24 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_MCO1Config), (28 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_MCO2Config), (28 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PCLK1Config), (24 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PCLK2Config), (24 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PLLCmd), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PLLConfig), (36 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PLLI2SCmd), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PLLI2SConfig), (16 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PLLSAICmd), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PLLSAIConfig), (24 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_RTCCLKCmd), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_RTCCLKConfig), (60 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_SAIBlockACLKConfig), (24 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_SAIBlockBCLKConfig), (24 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_SAIPLLI2SClkDivConfig), (28 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_SAIPLLSAIClkDivConfig), (28 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_SYSCLKConfig), (24 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_TIMCLKPresConfig), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_WaitForHSEStartUp), (56 bytes).
    Removing stm32f4xx_syscfg.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_syscfg.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_syscfg.o(i.SYSCFG_CompensationCellCmd), (12 bytes).
    Removing stm32f4xx_syscfg.o(i.SYSCFG_DeInit), (22 bytes).
    Removing stm32f4xx_syscfg.o(i.SYSCFG_ETH_MediaInterfaceConfig), (12 bytes).
    Removing stm32f4xx_syscfg.o(i.SYSCFG_EXTILineConfig), (64 bytes).
    Removing stm32f4xx_syscfg.o(i.SYSCFG_GetCompensationCellStatus), (24 bytes).
    Removing stm32f4xx_syscfg.o(i.SYSCFG_MemoryRemapConfig), (12 bytes).
    Removing stm32f4xx_syscfg.o(i.SYSCFG_MemorySwappingBank), (12 bytes).
    Removing stm32f4xx_usart.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_usart.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_usart.o(i.USART_ClearFlag), (18 bytes).
    Removing stm32f4xx_usart.o(i.USART_ClearITPendingBit), (30 bytes).
    Removing stm32f4xx_usart.o(i.USART_ClockInit), (32 bytes).
    Removing stm32f4xx_usart.o(i.USART_ClockStructInit), (12 bytes).
    Removing stm32f4xx_usart.o(i.USART_DMACmd), (18 bytes).
    Removing stm32f4xx_usart.o(i.USART_DeInit), (240 bytes).
    Removing stm32f4xx_usart.o(i.USART_GetFlagStatus), (26 bytes).
    Removing stm32f4xx_usart.o(i.USART_HalfDuplexCmd), (24 bytes).
    Removing stm32f4xx_usart.o(i.USART_IrDACmd), (24 bytes).
    Removing stm32f4xx_usart.o(i.USART_IrDAConfig), (18 bytes).
    Removing stm32f4xx_usart.o(i.USART_LINBreakDetectLengthConfig), (18 bytes).
    Removing stm32f4xx_usart.o(i.USART_LINCmd), (24 bytes).
    Removing stm32f4xx_usart.o(i.USART_OneBitMethodCmd), (24 bytes).
    Removing stm32f4xx_usart.o(i.USART_OverSampling8Cmd), (22 bytes).
    Removing stm32f4xx_usart.o(i.USART_ReceiverWakeUpCmd), (24 bytes).
    Removing stm32f4xx_usart.o(i.USART_SendBreak), (10 bytes).
    Removing stm32f4xx_usart.o(i.USART_SendData), (8 bytes).
    Removing stm32f4xx_usart.o(i.USART_SetAddress), (18 bytes).
    Removing stm32f4xx_usart.o(i.USART_SetGuardTime), (16 bytes).
    Removing stm32f4xx_usart.o(i.USART_SetPrescaler), (16 bytes).
    Removing stm32f4xx_usart.o(i.USART_SmartCardCmd), (24 bytes).
    Removing stm32f4xx_usart.o(i.USART_SmartCardNACKCmd), (24 bytes).
    Removing stm32f4xx_usart.o(i.USART_StructInit), (24 bytes).
    Removing stm32f4xx_usart.o(i.USART_WakeUpConfig), (18 bytes).
    Removing stm32f4xx_adc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_adc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_adc.o(i.ADC_AnalogWatchdogCmd), (20 bytes).
    Removing stm32f4xx_adc.o(i.ADC_AnalogWatchdogSingleChannelConfig), (16 bytes).
    Removing stm32f4xx_adc.o(i.ADC_AnalogWatchdogThresholdsConfig), (6 bytes).
    Removing stm32f4xx_adc.o(i.ADC_AutoInjectedConvCmd), (22 bytes).
    Removing stm32f4xx_adc.o(i.ADC_ClearFlag), (6 bytes).
    Removing stm32f4xx_adc.o(i.ADC_ClearITPendingBit), (10 bytes).
    Removing stm32f4xx_adc.o(i.ADC_CommonStructInit), (12 bytes).
    Removing stm32f4xx_adc.o(i.ADC_ContinuousModeCmd), (22 bytes).
    Removing stm32f4xx_adc.o(i.ADC_DeInit), (22 bytes).
    Removing stm32f4xx_adc.o(i.ADC_DiscModeChannelCountConfig), (24 bytes).
    Removing stm32f4xx_adc.o(i.ADC_DiscModeCmd), (22 bytes).
    Removing stm32f4xx_adc.o(i.ADC_EOCOnEachRegularChannelCmd), (22 bytes).
    Removing stm32f4xx_adc.o(i.ADC_ExternalTrigInjectedConvConfig), (16 bytes).
    Removing stm32f4xx_adc.o(i.ADC_ExternalTrigInjectedConvEdgeConfig), (16 bytes).
    Removing stm32f4xx_adc.o(i.ADC_GetConversionValue), (8 bytes).
    Removing stm32f4xx_adc.o(i.ADC_GetFlagStatus), (18 bytes).
    Removing stm32f4xx_adc.o(i.ADC_GetITStatus), (38 bytes).
    Removing stm32f4xx_adc.o(i.ADC_GetInjectedConversionValue), (28 bytes).
    Removing stm32f4xx_adc.o(i.ADC_GetMultiModeConversionValue), (12 bytes).
    Removing stm32f4xx_adc.o(i.ADC_GetSoftwareStartConvStatus), (20 bytes).
    Removing stm32f4xx_adc.o(i.ADC_GetSoftwareStartInjectedConvCmdStatus), (20 bytes).
    Removing stm32f4xx_adc.o(i.ADC_ITConfig), (32 bytes).
    Removing stm32f4xx_adc.o(i.ADC_InjectedChannelConfig), (130 bytes).
    Removing stm32f4xx_adc.o(i.ADC_InjectedDiscModeCmd), (22 bytes).
    Removing stm32f4xx_adc.o(i.ADC_InjectedSequencerLengthConfig), (24 bytes).
    Removing stm32f4xx_adc.o(i.ADC_MultiModeDMARequestAfterLastTransferCmd), (44 bytes).
    Removing stm32f4xx_adc.o(i.ADC_SetInjectedOffset), (20 bytes).
    Removing stm32f4xx_adc.o(i.ADC_SoftwareStartConv), (10 bytes).
    Removing stm32f4xx_adc.o(i.ADC_SoftwareStartInjectedConv), (10 bytes).
    Removing stm32f4xx_adc.o(i.ADC_StructInit), (20 bytes).
    Removing stm32f4xx_adc.o(i.ADC_TempSensorVrefintCmd), (44 bytes).
    Removing stm32f4xx_adc.o(i.ADC_VBATCmd), (44 bytes).
    Removing stm32f4xx_dma.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_dma.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_dma.o(i.DMA_ClearFlag), (52 bytes).
    Removing stm32f4xx_dma.o(i.DMA_DeInit), (344 bytes).
    Removing stm32f4xx_dma.o(i.DMA_DoubleBufferModeCmd), (22 bytes).
    Removing stm32f4xx_dma.o(i.DMA_DoubleBufferModeConfig), (24 bytes).
    Removing stm32f4xx_dma.o(i.DMA_FlowControllerConfig), (22 bytes).
    Removing stm32f4xx_dma.o(i.DMA_GetCmdStatus), (20 bytes).
    Removing stm32f4xx_dma.o(i.DMA_GetCurrDataCounter), (8 bytes).
    Removing stm32f4xx_dma.o(i.DMA_GetCurrentMemoryTarget), (20 bytes).
    Removing stm32f4xx_dma.o(i.DMA_GetFIFOStatus), (12 bytes).
    Removing stm32f4xx_dma.o(i.DMA_GetFlagStatus), (68 bytes).
    Removing stm32f4xx_dma.o(i.DMA_MemoryTargetConfig), (10 bytes).
    Removing stm32f4xx_dma.o(i.DMA_PeriphIncOffsetSizeConfig), (22 bytes).
    Removing stm32f4xx_dma.o(i.DMA_SetCurrDataCounter), (4 bytes).
    Removing stm32f4xx_dma.o(i.DMA_StructInit), (34 bytes).
    Removing stm32f4xx_tim.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_tim.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_tim.o(i.TI1_Config), (58 bytes).
    Removing stm32f4xx_tim.o(i.TI2_Config), (80 bytes).
    Removing stm32f4xx_tim.o(i.TI3_Config), (72 bytes).
    Removing stm32f4xx_tim.o(i.TI4_Config), (80 bytes).
    Removing stm32f4xx_tim.o(i.TIM_BDTRStructInit), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_CCPreloadControl), (24 bytes).
    Removing stm32f4xx_tim.o(i.TIM_CCxCmd), (30 bytes).
    Removing stm32f4xx_tim.o(i.TIM_CCxNCmd), (30 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ClearFlag), (6 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ClearITPendingBit), (6 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ClearOC1Ref), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ClearOC2Ref), (24 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ClearOC3Ref), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ClearOC4Ref), (24 bytes).
    Removing stm32f4xx_tim.o(i.TIM_CounterModeConfig), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_DMACmd), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_DMAConfig), (10 bytes).
    Removing stm32f4xx_tim.o(i.TIM_DeInit), (400 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ETRClockMode1Config), (54 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ETRClockMode2Config), (32 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ETRConfig), (28 bytes).
    Removing stm32f4xx_tim.o(i.TIM_EncoderInterfaceConfig), (66 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ForcedOC1Config), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ForcedOC2Config), (26 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ForcedOC3Config), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ForcedOC4Config), (26 bytes).
    Removing stm32f4xx_tim.o(i.TIM_GetCapture1), (6 bytes).
    Removing stm32f4xx_tim.o(i.TIM_GetCapture2), (6 bytes).
    Removing stm32f4xx_tim.o(i.TIM_GetCapture3), (6 bytes).
    Removing stm32f4xx_tim.o(i.TIM_GetCapture4), (6 bytes).
    Removing stm32f4xx_tim.o(i.TIM_GetCounter), (6 bytes).
    Removing stm32f4xx_tim.o(i.TIM_GetFlagStatus), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_GetITStatus), (34 bytes).
    Removing stm32f4xx_tim.o(i.TIM_GetPrescaler), (6 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ICInit), (110 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ICStructInit), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ITConfig), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ITRxExternalClockConfig), (24 bytes).
    Removing stm32f4xx_tim.o(i.TIM_InternalClockConfig), (12 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC1FastConfig), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC1NPolarityConfig), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC1PolarityConfig), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC2FastConfig), (26 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC2NPolarityConfig), (26 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC2PolarityConfig), (26 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC3FastConfig), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC3NPolarityConfig), (26 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC3PolarityConfig), (26 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC4FastConfig), (26 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC4Init), (120 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC4PolarityConfig), (26 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC4PreloadConfig), (26 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OCStructInit), (20 bytes).
    Removing stm32f4xx_tim.o(i.TIM_PWMIConfig), (124 bytes).
    Removing stm32f4xx_tim.o(i.TIM_PrescalerConfig), (6 bytes).
    Removing stm32f4xx_tim.o(i.TIM_RemapConfig), (6 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SelectCCDMA), (24 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SelectCOM), (24 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SelectHallSensor), (24 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SelectInputTrigger), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SelectMasterSlaveMode), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SelectOCxM), (86 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SelectOnePulseMode), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SelectSlaveMode), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetClockDivision), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetCompare1), (4 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetCompare2), (4 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetCompare3), (4 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetCompare4), (4 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetCounter), (4 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetIC1Prescaler), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetIC2Prescaler), (26 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetIC3Prescaler), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetIC4Prescaler), (26 bytes).
    Removing stm32f4xx_tim.o(i.TIM_TIxExternalClockConfig), (62 bytes).
    Removing stm32f4xx_tim.o(i.TIM_TimeBaseStructInit), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_UpdateDisableConfig), (24 bytes).
    Removing stm32f4xx_tim.o(i.TIM_UpdateRequestConfig), (24 bytes).
    Removing stm32f4xx_dma2d.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_dma2d.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_AbortTransfer), (20 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_BGConfig), (184 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_BGStart), (36 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_BG_StructInit), (26 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_ClearFlag), (12 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_ClearITPendingBit), (12 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_DeInit), (22 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_DeadTimeConfig), (56 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_FGConfig), (184 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_FGStart), (36 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_FG_StructInit), (26 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_GetFlagStatus), (24 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_GetITStatus), (44 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_ITConfig), (32 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_Init), (228 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_LineWatermarkConfig), (12 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_StartTransfer), (20 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_StructInit), (24 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_Suspend), (36 bytes).
    Removing arm_cos_f32.o(.rev16_text), (4 bytes).
    Removing arm_cos_f32.o(.revsh_text), (4 bytes).
    Removing arm_cos_f32.o(.text), (216 bytes).
    Removing arm_cos_f32.o(.constdata), (1040 bytes).
    Removing arm_sin_f32.o(.rev16_text), (4 bytes).
    Removing arm_sin_f32.o(.revsh_text), (4 bytes).
    Removing arm_sin_f32.o(.text), (216 bytes).
    Removing arm_sin_f32.o(.constdata), (1036 bytes).

313 unused section(s) (total 13336 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_copy.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi_2.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/fenv.c                           0x00000000   Number         0  _rserrno.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../fplib/basic.s                         0x00000000   Number         0  basic.o ABSOLUTE
    ../fplib/d2f.s                           0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/daddsub.s                       0x00000000   Number         0  daddsub_clz.o ABSOLUTE
    ../fplib/dcmpi.s                         0x00000000   Number         0  dcmpi.o ABSOLUTE
    ../fplib/ddiv.s                          0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/dfix.s                          0x00000000   Number         0  dfix.o ABSOLUTE
    ../fplib/dflt.s                          0x00000000   Number         0  dflt_clz.o ABSOLUTE
    ../fplib/dleqf.s                         0x00000000   Number         0  dleqf.o ABSOLUTE
    ../fplib/dmul.s                          0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/dnaninf.s                       0x00000000   Number         0  dnaninf.o ABSOLUTE
    ../fplib/dretinf.s                       0x00000000   Number         0  dretinf.o ABSOLUTE
    ../fplib/f2d.s                           0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/fnaninf.s                       0x00000000   Number         0  fnaninf.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/fretinf.s                       0x00000000   Number         0  fretinf.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    ../mathlib/cos_i.c                       0x00000000   Number         0  cos_i.o ABSOLUTE
    ../mathlib/dunder.c                      0x00000000   Number         0  dunder.o ABSOLUTE
    ../mathlib/fabs.c                        0x00000000   Number         0  fabs.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ../mathlib/poly.c                        0x00000000   Number         0  poly.o ABSOLUTE
    ../mathlib/rred.c                        0x00000000   Number         0  rred.o ABSOLUTE
    ../mathlib/sin.c                         0x00000000   Number         0  sin_x.o ABSOLUTE
    ../mathlib/sin.c                         0x00000000   Number         0  sin.o ABSOLUTE
    ../mathlib/sin_i.c                       0x00000000   Number         0  sin_i.o ABSOLUTE
    ../mathlib/sin_i.c                       0x00000000   Number         0  sin_i_x.o ABSOLUTE
    ..\CORE\startup_stm32f40_41xxx.s         0x00000000   Number         0  startup_stm32f40_41xxx.o ABSOLUTE
    ..\FWLIB\src\misc.c                      0x00000000   Number         0  misc.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_adc.c             0x00000000   Number         0  stm32f4xx_adc.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_dma.c             0x00000000   Number         0  stm32f4xx_dma.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_dma2d.c           0x00000000   Number         0  stm32f4xx_dma2d.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_gpio.c            0x00000000   Number         0  stm32f4xx_gpio.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_rcc.c             0x00000000   Number         0  stm32f4xx_rcc.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_syscfg.c          0x00000000   Number         0  stm32f4xx_syscfg.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_tim.c             0x00000000   Number         0  stm32f4xx_tim.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_usart.c           0x00000000   Number         0  stm32f4xx_usart.o ABSOLUTE
    ..\FastMathFunctions\arm_cos_f32.c       0x00000000   Number         0  arm_cos_f32.o ABSOLUTE
    ..\FastMathFunctions\arm_sin_f32.c       0x00000000   Number         0  arm_sin_f32.o ABSOLUTE
    ..\HARDWARE\KEY\key.c                    0x00000000   Number         0  key.o ABSOLUTE
    ..\HARDWARE\LED\led.c                    0x00000000   Number         0  led.o ABSOLUTE
    ..\HARDWARE\ad\AD.c                      0x00000000   Number         0  ad.o ABSOLUTE
    ..\HARDWARE\inveter\inveter.c            0x00000000   Number         0  inveter.o ABSOLUTE
    ..\HARDWARE\oled\OLED.c                  0x00000000   Number         0  oled.o ABSOLUTE
    ..\HARDWARE\pfc\pfc.c                    0x00000000   Number         0  pfc.o ABSOLUTE
    ..\HARDWARE\show\show.c                  0x00000000   Number         0  show.o ABSOLUTE
    ..\HARDWARE\sine\sine.c                  0x00000000   Number         0  sine.o ABSOLUTE
    ..\HARDWARE\spwm\timer1.c                0x00000000   Number         0  timer1.o ABSOLUTE
    ..\HARDWARE\vofa\vofa.c                  0x00000000   Number         0  vofa.o ABSOLUTE
    ..\SYSTEM\delay\delay.c                  0x00000000   Number         0  delay.o ABSOLUTE
    ..\SYSTEM\sys\sys.c                      0x00000000   Number         0  sys.o ABSOLUTE
    ..\SYSTEM\usart\usart.c                  0x00000000   Number         0  usart.o ABSOLUTE
    ..\\FWLIB\\src\\misc.c                   0x00000000   Number         0  misc.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_adc.c          0x00000000   Number         0  stm32f4xx_adc.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_dma.c          0x00000000   Number         0  stm32f4xx_dma.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_dma2d.c        0x00000000   Number         0  stm32f4xx_dma2d.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_gpio.c         0x00000000   Number         0  stm32f4xx_gpio.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_rcc.c          0x00000000   Number         0  stm32f4xx_rcc.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_syscfg.c       0x00000000   Number         0  stm32f4xx_syscfg.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_tim.c          0x00000000   Number         0  stm32f4xx_tim.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_usart.c        0x00000000   Number         0  stm32f4xx_usart.o ABSOLUTE
    ..\\FastMathFunctions\\arm_cos_f32.c     0x00000000   Number         0  arm_cos_f32.o ABSOLUTE
    ..\\FastMathFunctions\\arm_sin_f32.c     0x00000000   Number         0  arm_sin_f32.o ABSOLUTE
    ..\\HARDWARE\\KEY\\key.c                 0x00000000   Number         0  key.o ABSOLUTE
    ..\\HARDWARE\\LED\\led.c                 0x00000000   Number         0  led.o ABSOLUTE
    ..\\HARDWARE\\ad\\AD.c                   0x00000000   Number         0  ad.o ABSOLUTE
    ..\\HARDWARE\\inveter\\inveter.c         0x00000000   Number         0  inveter.o ABSOLUTE
    ..\\HARDWARE\\oled\\OLED.c               0x00000000   Number         0  oled.o ABSOLUTE
    ..\\HARDWARE\\pfc\\pfc.c                 0x00000000   Number         0  pfc.o ABSOLUTE
    ..\\HARDWARE\\show\\show.c               0x00000000   Number         0  show.o ABSOLUTE
    ..\\HARDWARE\\sine\\sine.c               0x00000000   Number         0  sine.o ABSOLUTE
    ..\\HARDWARE\\spwm\\timer1.c             0x00000000   Number         0  timer1.o ABSOLUTE
    ..\\HARDWARE\\vofa\\vofa.c               0x00000000   Number         0  vofa.o ABSOLUTE
    ..\\SYSTEM\\delay\\delay.c               0x00000000   Number         0  delay.o ABSOLUTE
    ..\\SYSTEM\\sys\\sys.c                   0x00000000   Number         0  sys.o ABSOLUTE
    ..\\SYSTEM\\usart\\usart.c               0x00000000   Number         0  usart.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    main.c                                   0x00000000   Number         0  main.o ABSOLUTE
    main.c                                   0x00000000   Number         0  main.o ABSOLUTE
    stm32f4xx_it.c                           0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    stm32f4xx_it.c                           0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    system_stm32f4xx.c                       0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    system_stm32f4xx.c                       0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    RESET                                    0x08000000   Section      392  startup_stm32f40_41xxx.o(RESET)
    !!!main                                  0x08000188   Section        8  __main.o(!!!main)
    !!!scatter                               0x08000190   Section       52  __scatter.o(!!!scatter)
    !!handler_copy                           0x080001c4   Section       26  __scatter_copy.o(!!handler_copy)
    !!handler_zi                             0x080001e0   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$libinit$$00000000          0x080001fc   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000001          0x080001fe   Section        4  libinit2.o(.ARM.Collect$$libinit$$00000001)
    .ARM.Collect$$libinit$$00000004          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$0000000A          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$00000011          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000013          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000017          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x08000202   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x08000202   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x08000204   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x08000206   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x08000206   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000007      0x08000206   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    .ARM.Collect$$libshutdown$$0000000A      0x08000206   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    .ARM.Collect$$libshutdown$$0000000C      0x08000206   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000F      0x08000206   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    .ARM.Collect$$libshutdown$$00000010      0x08000206   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    .ARM.Collect$$rtentry$$00000000          0x08000208   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x08000208   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x08000208   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x0800020e   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x0800020e   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x08000212   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x08000212   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x0800021a   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x0800021c   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x0800021c   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x08000220   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .text                                    0x08000228   Section       64  startup_stm32f40_41xxx.o(.text)
    $v0                                      0x08000228   Number         0  startup_stm32f40_41xxx.o(.text)
    .text                                    0x08000268   Section        2  use_no_semi_2.o(.text)
    .text                                    0x0800026a   Section        0  heapauxi.o(.text)
    .text                                    0x08000270   Section        2  use_no_semi.o(.text)
    .text                                    0x08000272   Section        0  _rserrno.o(.text)
    .text                                    0x08000288   Section        8  rt_errno_addr_intlibspace.o(.text)
    .text                                    0x08000290   Section        8  libspace.o(.text)
    .text                                    0x08000298   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x080002e2   Section        0  exit.o(.text)
    i.ADC_Cmd                                0x080002f4   Section        0  stm32f4xx_adc.o(i.ADC_Cmd)
    i.ADC_CommonInit                         0x0800030c   Section        0  stm32f4xx_adc.o(i.ADC_CommonInit)
    i.ADC_DMACmd                             0x0800033c   Section        0  stm32f4xx_adc.o(i.ADC_DMACmd)
    i.ADC_DMARequestAfterLastTransferCmd     0x08000352   Section        0  stm32f4xx_adc.o(i.ADC_DMARequestAfterLastTransferCmd)
    i.ADC_Init                               0x08000368   Section        0  stm32f4xx_adc.o(i.ADC_Init)
    i.ADC_RegularChannelConfig               0x080003bc   Section        0  stm32f4xx_adc.o(i.ADC_RegularChannelConfig)
    i.ARR_adjust                             0x08000474   Section        0  show.o(i.ARR_adjust)
    i.BusFault_Handler                       0x080004e4   Section        0  stm32f4xx_it.o(i.BusFault_Handler)
    i.DMA2_Stream0_IRQHandler                0x080004e8   Section        0  main.o(i.DMA2_Stream0_IRQHandler)
    i.DMA_ClearITPendingBit                  0x0800074c   Section        0  stm32f4xx_dma.o(i.DMA_ClearITPendingBit)
    i.DMA_Cmd                                0x08000780   Section        0  stm32f4xx_dma.o(i.DMA_Cmd)
    i.DMA_GetITStatus                        0x08000798   Section        0  stm32f4xx_dma.o(i.DMA_GetITStatus)
    i.DMA_ITConfig                           0x080007fc   Section        0  stm32f4xx_dma.o(i.DMA_ITConfig)
    i.DMA_Init                               0x08000838   Section        0  stm32f4xx_dma.o(i.DMA_Init)
    i.DebugMon_Handler                       0x08000890   Section        0  stm32f4xx_it.o(i.DebugMon_Handler)
    i.GPIO_Init                              0x08000892   Section        0  stm32f4xx_gpio.o(i.GPIO_Init)
    i.GPIO_PinAFConfig                       0x08000922   Section        0  stm32f4xx_gpio.o(i.GPIO_PinAFConfig)
    i.GPIO_ReadInputDataBit                  0x08000968   Section        0  stm32f4xx_gpio.o(i.GPIO_ReadInputDataBit)
    i.GPIO_ResetBits                         0x0800097a   Section        0  stm32f4xx_gpio.o(i.GPIO_ResetBits)
    i.GPIO_SetBits                           0x0800097e   Section        0  stm32f4xx_gpio.o(i.GPIO_SetBits)
    i.GPIO_WriteBit                          0x08000982   Section        0  stm32f4xx_gpio.o(i.GPIO_WriteBit)
    i.HardFault_Handler                      0x0800098c   Section        0  stm32f4xx_it.o(i.HardFault_Handler)
    i.KEY_Calculate                          0x08000990   Section        0  key.o(i.KEY_Calculate)
    i.KEY_Init                               0x08000c6c   Section        0  key.o(i.KEY_Init)
    i.KEY_Scan1                              0x08000cac   Section        0  key.o(i.KEY_Scan1)
    i.KEY_Scan3                              0x08000d68   Section        0  key.o(i.KEY_Scan3)
    i.LED_Init                               0x08000db4   Section        0  led.o(i.LED_Init)
    i.MemManage_Handler                      0x08000e14   Section        0  stm32f4xx_it.o(i.MemManage_Handler)
    i.MyADC1_Init                            0x08000e18   Section        0  ad.o(i.MyADC1_Init)
    i.MyDMA1_Init                            0x08000f08   Section        0  ad.o(i.MyDMA1_Init)
    i.MyTIM3_Init                            0x08000fbc   Section        0  ad.o(i.MyTIM3_Init)
    i.NMI_Handler                            0x08001004   Section        0  stm32f4xx_it.o(i.NMI_Handler)
    i.NVIC_Init                              0x08001008   Section        0  misc.o(i.NVIC_Init)
    i.NVIC_PriorityGroupConfig               0x08001080   Section        0  misc.o(i.NVIC_PriorityGroupConfig)
    i.OLED_Clear                             0x08001094   Section        0  oled.o(i.OLED_Clear)
    i.OLED_Float                             0x080010c0   Section        0  oled.o(i.OLED_Float)
    i.OLED_I2C_Init                          0x080012b0   Section        0  oled.o(i.OLED_I2C_Init)
    i.OLED_I2C_SendByte                      0x0800130c   Section        0  oled.o(i.OLED_I2C_SendByte)
    i.OLED_I2C_Start                         0x08001378   Section        0  oled.o(i.OLED_I2C_Start)
    i.OLED_I2C_Stop                          0x080013b8   Section        0  oled.o(i.OLED_I2C_Stop)
    i.OLED_Init                              0x080013f4   Section        0  oled.o(i.OLED_Init)
    i.OLED_SetCursor                         0x08001492   Section        0  oled.o(i.OLED_SetCursor)
    i.OLED_ShowChar                          0x080014b4   Section        0  oled.o(i.OLED_ShowChar)
    i.OLED_ShowString                        0x08001520   Section        0  oled.o(i.OLED_ShowString)
    i.OLED_WriteCommand                      0x08001548   Section        0  oled.o(i.OLED_WriteCommand)
    i.OLED_WriteData                         0x08001568   Section        0  oled.o(i.OLED_WriteData)
    i.PendSV_Handler                         0x08001588   Section        0  stm32f4xx_it.o(i.PendSV_Handler)
    i.RCC_AHB1PeriphClockCmd                 0x0800158c   Section        0  stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd)
    i.RCC_APB1PeriphClockCmd                 0x080015ac   Section        0  stm32f4xx_rcc.o(i.RCC_APB1PeriphClockCmd)
    i.RCC_APB2PeriphClockCmd                 0x080015cc   Section        0  stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd)
    i.RCC_APB2PeriphResetCmd                 0x080015ec   Section        0  stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd)
    i.RCC_GetClocksFreq                      0x0800160c   Section        0  stm32f4xx_rcc.o(i.RCC_GetClocksFreq)
    i.SVC_Handler                            0x080016f4   Section        0  stm32f4xx_it.o(i.SVC_Handler)
    i.SetSysClock                            0x080016f8   Section        0  system_stm32f4xx.o(i.SetSysClock)
    SetSysClock                              0x080016f9   Thumb Code   220  system_stm32f4xx.o(i.SetSysClock)
    i.SysTick_CLKSourceConfig                0x080017e4   Section        0  misc.o(i.SysTick_CLKSourceConfig)
    i.SysTick_Handler                        0x0800180c   Section        0  stm32f4xx_it.o(i.SysTick_Handler)
    i.SystemInit                             0x08001810   Section        0  system_stm32f4xx.o(i.SystemInit)
    i.TIM1_PWM_CHN_Init                      0x08001878   Section        0  timer1.o(i.TIM1_PWM_CHN_Init)
    i.TIM8_PWM_CHN_Init                      0x080019c0   Section        0  timer1.o(i.TIM8_PWM_CHN_Init)
    i.TIM_ARRPreloadConfig                   0x08001b28   Section        0  stm32f4xx_tim.o(i.TIM_ARRPreloadConfig)
    i.TIM_BDTRConfig                         0x08001b40   Section        0  stm32f4xx_tim.o(i.TIM_BDTRConfig)
    i.TIM_Cmd                                0x08001b60   Section        0  stm32f4xx_tim.o(i.TIM_Cmd)
    i.TIM_CtrlPWMOutputs                     0x08001b78   Section        0  stm32f4xx_tim.o(i.TIM_CtrlPWMOutputs)
    i.TIM_GenerateEvent                      0x08001b96   Section        0  stm32f4xx_tim.o(i.TIM_GenerateEvent)
    i.TIM_OC1Init                            0x08001b9c   Section        0  stm32f4xx_tim.o(i.TIM_OC1Init)
    i.TIM_OC1PreloadConfig                   0x08001c18   Section        0  stm32f4xx_tim.o(i.TIM_OC1PreloadConfig)
    i.TIM_OC2Init                            0x08001c2c   Section        0  stm32f4xx_tim.o(i.TIM_OC2Init)
    i.TIM_OC2PreloadConfig                   0x08001cd0   Section        0  stm32f4xx_tim.o(i.TIM_OC2PreloadConfig)
    i.TIM_OC3Init                            0x08001cec   Section        0  stm32f4xx_tim.o(i.TIM_OC3Init)
    i.TIM_OC3PreloadConfig                   0x08001d8c   Section        0  stm32f4xx_tim.o(i.TIM_OC3PreloadConfig)
    i.TIM_SelectOutputTrigger                0x08001d9e   Section        0  stm32f4xx_tim.o(i.TIM_SelectOutputTrigger)
    i.TIM_SetAutoreload                      0x08001db0   Section        0  stm32f4xx_tim.o(i.TIM_SetAutoreload)
    i.TIM_TimeBaseInit                       0x08001db4   Section        0  stm32f4xx_tim.o(i.TIM_TimeBaseInit)
    i.USART1_IRQHandler                      0x08001e38   Section        0  usart.o(i.USART1_IRQHandler)
    i.USART_Cmd                              0x08001ec0   Section        0  stm32f4xx_usart.o(i.USART_Cmd)
    i.USART_GetITStatus                      0x08001ed8   Section        0  stm32f4xx_usart.o(i.USART_GetITStatus)
    i.USART_ITConfig                         0x08001f2c   Section        0  stm32f4xx_usart.o(i.USART_ITConfig)
    i.USART_Init                             0x08001f78   Section        0  stm32f4xx_usart.o(i.USART_Init)
    i.USART_ReceiveData                      0x0800204c   Section        0  stm32f4xx_usart.o(i.USART_ReceiveData)
    i.UsageFault_Handler                     0x08002056   Section        0  stm32f4xx_it.o(i.UsageFault_Handler)
    i.__ARM_fpclassify                       0x0800205a   Section        0  fpclassify.o(i.__ARM_fpclassify)
    i.__hardfp_sin                           0x08002090   Section        0  sin.o(i.__hardfp_sin)
    i.__ieee754_rem_pio2                     0x08002158   Section        0  rred.o(i.__ieee754_rem_pio2)
    i.__kernel_cos                           0x08002590   Section        0  cos_i.o(i.__kernel_cos)
    i.__kernel_poly                          0x08002700   Section        0  poly.o(i.__kernel_poly)
    i.__kernel_sin                           0x080027f8   Section        0  sin_i.o(i.__kernel_sin)
    i.__mathlib_dbl_infnan                   0x08002928   Section        0  dunder.o(i.__mathlib_dbl_infnan)
    i.__mathlib_dbl_invalid                  0x08002940   Section        0  dunder.o(i.__mathlib_dbl_invalid)
    i.__mathlib_dbl_underflow                0x08002960   Section        0  dunder.o(i.__mathlib_dbl_underflow)
    i._sys_exit                              0x08002980   Section        0  usart.o(i._sys_exit)
    i.delay_init                             0x08002984   Section        0  delay.o(i.delay_init)
    i.delay_ms                               0x080029c0   Section        0  delay.o(i.delay_ms)
    i.delay_us                               0x080029f8   Section        0  delay.o(i.delay_us)
    i.delay_xms                              0x08002a44   Section        0  delay.o(i.delay_xms)
    i.fabs                                   0x08002a90   Section        0  fabs.o(i.fabs)
    i.get_sin_tab1                           0x08002aa8   Section        0  sine.o(i.get_sin_tab1)
    i.invt_init                              0x08002b40   Section        0  inveter.o(i.invt_init)
    i.invt_loop                              0x08002bf4   Section        0  inveter.o(i.invt_loop)
    i.main                                   0x08002da4   Section        0  main.o(i.main)
    i.pfc_init                               0x08002ef0   Section        0  pfc.o(i.pfc_init)
    i.show2                                  0x08002fd4   Section        0  show.o(i.show2)
    i.show_AD                                0x08003034   Section        0  show.o(i.show_AD)
    i.uart_init                              0x08003080   Section        0  usart.o(i.uart_init)
    i.update_TIM_ARR                         0x08003130   Section        0  show.o(i.update_TIM_ARR)
    x$fpl$basic                              0x08003164   Section       24  basic.o(x$fpl$basic)
    $v0                                      0x08003164   Number         0  basic.o(x$fpl$basic)
    x$fpl$d2f                                0x0800317c   Section       98  d2f.o(x$fpl$d2f)
    $v0                                      0x0800317c   Number         0  d2f.o(x$fpl$d2f)
    x$fpl$dadd                               0x080031e0   Section      336  daddsub_clz.o(x$fpl$dadd)
    $v0                                      0x080031e0   Number         0  daddsub_clz.o(x$fpl$dadd)
    _dadd1                                   0x080031f1   Thumb Code     0  daddsub_clz.o(x$fpl$dadd)
    x$fpl$dcmpinf                            0x08003330   Section       24  dcmpi.o(x$fpl$dcmpinf)
    $v0                                      0x08003330   Number         0  dcmpi.o(x$fpl$dcmpinf)
    x$fpl$ddiv                               0x08003348   Section      688  ddiv.o(x$fpl$ddiv)
    $v0                                      0x08003348   Number         0  ddiv.o(x$fpl$ddiv)
    ddiv_entry                               0x0800334f   Thumb Code     0  ddiv.o(x$fpl$ddiv)
    x$fpl$dfix                               0x080035f8   Section       94  dfix.o(x$fpl$dfix)
    $v0                                      0x080035f8   Number         0  dfix.o(x$fpl$dfix)
    x$fpl$dflt                               0x08003656   Section       46  dflt_clz.o(x$fpl$dflt)
    $v0                                      0x08003656   Number         0  dflt_clz.o(x$fpl$dflt)
    x$fpl$dfltu                              0x08003684   Section       38  dflt_clz.o(x$fpl$dfltu)
    $v0                                      0x08003684   Number         0  dflt_clz.o(x$fpl$dfltu)
    x$fpl$dleqf                              0x080036ac   Section      120  dleqf.o(x$fpl$dleqf)
    $v0                                      0x080036ac   Number         0  dleqf.o(x$fpl$dleqf)
    x$fpl$dmul                               0x08003724   Section      340  dmul.o(x$fpl$dmul)
    $v0                                      0x08003724   Number         0  dmul.o(x$fpl$dmul)
    x$fpl$dnaninf                            0x08003878   Section      156  dnaninf.o(x$fpl$dnaninf)
    $v0                                      0x08003878   Number         0  dnaninf.o(x$fpl$dnaninf)
    x$fpl$dretinf                            0x08003914   Section       12  dretinf.o(x$fpl$dretinf)
    $v0                                      0x08003914   Number         0  dretinf.o(x$fpl$dretinf)
    x$fpl$drsb                               0x08003920   Section       22  daddsub_clz.o(x$fpl$drsb)
    $v0                                      0x08003920   Number         0  daddsub_clz.o(x$fpl$drsb)
    x$fpl$dsub                               0x08003938   Section      468  daddsub_clz.o(x$fpl$dsub)
    $v0                                      0x08003938   Number         0  daddsub_clz.o(x$fpl$dsub)
    _dsub1                                   0x08003949   Thumb Code     0  daddsub_clz.o(x$fpl$dsub)
    x$fpl$f2d                                0x08003b0c   Section       86  f2d.o(x$fpl$f2d)
    $v0                                      0x08003b0c   Number         0  f2d.o(x$fpl$f2d)
    x$fpl$fnaninf                            0x08003b62   Section      140  fnaninf.o(x$fpl$fnaninf)
    $v0                                      0x08003b62   Number         0  fnaninf.o(x$fpl$fnaninf)
    x$fpl$fpinit                             0x08003bee   Section       10  fpinit.o(x$fpl$fpinit)
    $v0                                      0x08003bee   Number         0  fpinit.o(x$fpl$fpinit)
    x$fpl$fretinf                            0x08003bf8   Section       10  fretinf.o(x$fpl$fretinf)
    $v0                                      0x08003bf8   Number         0  fretinf.o(x$fpl$fretinf)
    .constdata                               0x08003c02   Section     1520  oled.o(.constdata)
    x$fpl$usenofp                            0x08003c02   Section        0  usenofp.o(x$fpl$usenofp)
    .constdata                               0x080041f2   Section      162  show.o(.constdata)
    .constdata                               0x08004298   Section       48  cos_i.o(.constdata)
    C                                        0x08004298   Data          48  cos_i.o(.constdata)
    .constdata                               0x080042c8   Section      200  rred.o(.constdata)
    pio2s                                    0x080042c8   Data          48  rred.o(.constdata)
    twooverpi                                0x080042f8   Data         152  rred.o(.constdata)
    .constdata                               0x08004390   Section       40  sin_i.o(.constdata)
    S                                        0x08004390   Data          40  sin_i.o(.constdata)
    .data                                    0x20000000   Section       48  main.o(.data)
    n                                        0x20000018   Data           4  main.o(.data)
    flag3                                    0x2000001c   Data           4  main.o(.data)
    k                                        0x20000020   Data           4  main.o(.data)
    max                                      0x20000024   Data           4  main.o(.data)
    min                                      0x20000028   Data           4  main.o(.data)
    a                                        0x2000002c   Data           4  main.o(.data)
    .data                                    0x20000030   Section       16  ad.o(.data)
    .data                                    0x20000040   Section       36  key.o(.data)
    key_up                                   0x20000040   Data           1  key.o(.data)
    key_up                                   0x20000041   Data           1  key.o(.data)
    key_up                                   0x20000044   Data           4  key.o(.data)
    i                                        0x20000048   Data           4  key.o(.data)
    flag                                     0x2000004c   Data           4  key.o(.data)
    cl0                                      0x20000050   Data           4  key.o(.data)
    cl1                                      0x20000054   Data           4  key.o(.data)
    cl2                                      0x20000058   Data           4  key.o(.data)
    cl3                                      0x2000005c   Data           4  key.o(.data)
    cl4                                      0x20000060   Data           4  key.o(.data)
    .data                                    0x20000064   Section        4  delay.o(.data)
    fac_us                                   0x20000064   Data           1  delay.o(.data)
    fac_ms                                   0x20000066   Data           2  delay.o(.data)
    .data                                    0x20000068   Section        6  usart.o(.data)
    .data                                    0x2000006e   Section       16  stm32f4xx_rcc.o(.data)
    APBAHBPrescTable                         0x2000006e   Data          16  stm32f4xx_rcc.o(.data)
    .bss                                     0x20000080   Section      332  main.o(.bss)
    .bss                                     0x200001cc   Section     1600  sine.o(.bss)
    .bss                                     0x2000080c   Section      200  usart.o(.bss)
    .bss                                     0x200008d4   Section       96  libspace.o(.bss)
    HEAP                                     0x20000938   Section      512  startup_stm32f40_41xxx.o(HEAP)
    Heap_Mem                                 0x20000938   Data         512  startup_stm32f40_41xxx.o(HEAP)
    STACK                                    0x20000b38   Section     1024  startup_stm32f40_41xxx.o(STACK)
    Stack_Mem                                0x20000b38   Data        1024  startup_stm32f40_41xxx.o(STACK)
    __initial_sp                             0x20000f38   Data           0  startup_stm32f40_41xxx.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __rt_locale                               - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_ctype                             - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_numeric                           - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_Size                           0x00000188   Number         0  startup_stm32f40_41xxx.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f40_41xxx.o(RESET)
    __Vectors_End                            0x08000188   Data           0  startup_stm32f40_41xxx.o(RESET)
    __main                                   0x08000189   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x08000191   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x08000191   Thumb Code    44  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x08000191   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x0800019f   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_copy                       0x080001c5   Thumb Code    26  __scatter_copy.o(!!handler_copy)
    __scatterload_zeroinit                   0x080001e1   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    __rt_lib_init                            0x080001fd   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_fp_1                       0x080001ff   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000001)
    __rt_lib_init_alloca_1                   0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_atexit_1                   0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_cpp_1                      0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_exceptions_1               0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_fp_trap_1                  0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_heap_1                     0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_lc_collate_1               0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_1                 0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_monetary_1              0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_1               0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_preinit_1                  0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_rand_1                     0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_return                     0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_init_signal_1                   0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_1                    0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_init_user_alloc_1               0x08000203   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_shutdown                        0x08000205   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x08000207   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x08000207   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    __rt_lib_shutdown_heap_1                 0x08000207   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    __rt_lib_shutdown_return                 0x08000207   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    __rt_lib_shutdown_signal_1               0x08000207   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    __rt_lib_shutdown_stdio_1                0x08000207   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_user_alloc_1           0x08000207   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_entry                               0x08000209   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x08000209   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x08000209   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x0800020f   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x0800020f   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x08000213   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x08000213   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x0800021b   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x0800021d   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x0800021d   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x08000221   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    Reset_Handler                            0x08000229   Thumb Code     8  startup_stm32f40_41xxx.o(.text)
    ADC_IRQHandler                           0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN1_RX0_IRQHandler                      0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN1_RX1_IRQHandler                      0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN1_SCE_IRQHandler                      0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN1_TX_IRQHandler                       0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN2_RX0_IRQHandler                      0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN2_RX1_IRQHandler                      0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN2_SCE_IRQHandler                      0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN2_TX_IRQHandler                       0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CRYP_IRQHandler                          0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DCMI_IRQHandler                          0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream0_IRQHandler                  0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream1_IRQHandler                  0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream2_IRQHandler                  0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream3_IRQHandler                  0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream4_IRQHandler                  0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream5_IRQHandler                  0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream6_IRQHandler                  0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream7_IRQHandler                  0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream1_IRQHandler                  0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream2_IRQHandler                  0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream3_IRQHandler                  0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream4_IRQHandler                  0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream5_IRQHandler                  0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream6_IRQHandler                  0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream7_IRQHandler                  0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    ETH_IRQHandler                           0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    ETH_WKUP_IRQHandler                      0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI0_IRQHandler                         0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI15_10_IRQHandler                     0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI1_IRQHandler                         0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI2_IRQHandler                         0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI3_IRQHandler                         0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI4_IRQHandler                         0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI9_5_IRQHandler                       0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    FLASH_IRQHandler                         0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    FPU_IRQHandler                           0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    FSMC_IRQHandler                          0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    HASH_RNG_IRQHandler                      0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C1_ER_IRQHandler                       0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C1_EV_IRQHandler                       0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C2_ER_IRQHandler                       0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C2_EV_IRQHandler                       0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C3_ER_IRQHandler                       0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C3_EV_IRQHandler                       0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_FS_IRQHandler                        0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_FS_WKUP_IRQHandler                   0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_HS_EP1_IN_IRQHandler                 0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_HS_EP1_OUT_IRQHandler                0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_HS_IRQHandler                        0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_HS_WKUP_IRQHandler                   0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    PVD_IRQHandler                           0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    RCC_IRQHandler                           0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    RTC_Alarm_IRQHandler                     0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    RTC_WKUP_IRQHandler                      0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    SDIO_IRQHandler                          0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    SPI1_IRQHandler                          0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    SPI2_IRQHandler                          0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    SPI3_IRQHandler                          0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TAMP_STAMP_IRQHandler                    0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM1_BRK_TIM9_IRQHandler                 0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM1_CC_IRQHandler                       0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM1_TRG_COM_TIM11_IRQHandler            0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM1_UP_TIM10_IRQHandler                 0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM2_IRQHandler                          0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM3_IRQHandler                          0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM4_IRQHandler                          0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM5_IRQHandler                          0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM6_DAC_IRQHandler                      0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM7_IRQHandler                          0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM8_BRK_TIM12_IRQHandler                0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM8_CC_IRQHandler                       0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM8_TRG_COM_TIM14_IRQHandler            0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM8_UP_TIM13_IRQHandler                 0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    UART4_IRQHandler                         0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    UART5_IRQHandler                         0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    USART2_IRQHandler                        0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    USART3_IRQHandler                        0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    USART6_IRQHandler                        0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    WWDG_IRQHandler                          0x08000243   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    __user_initial_stackheap                 0x08000245   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    __use_no_semihosting                     0x08000269   Thumb Code     2  use_no_semi_2.o(.text)
    __use_two_region_memory                  0x0800026b   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x0800026d   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x0800026f   Thumb Code     2  heapauxi.o(.text)
    __I$use$semihosting                      0x08000271   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x08000271   Thumb Code     2  use_no_semi.o(.text)
    __read_errno                             0x08000273   Thumb Code    10  _rserrno.o(.text)
    __set_errno                              0x0800027d   Thumb Code    12  _rserrno.o(.text)
    __aeabi_errno_addr                       0x08000289   Thumb Code     8  rt_errno_addr_intlibspace.o(.text)
    __errno$intlibspace                      0x08000289   Thumb Code     0  rt_errno_addr_intlibspace.o(.text)
    __rt_errno_addr$intlibspace              0x08000289   Thumb Code     0  rt_errno_addr_intlibspace.o(.text)
    __user_libspace                          0x08000291   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x08000291   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x08000291   Thumb Code     0  libspace.o(.text)
    __user_setup_stackheap                   0x08000299   Thumb Code    74  sys_stackheap_outer.o(.text)
    exit                                     0x080002e3   Thumb Code    18  exit.o(.text)
    ADC_Cmd                                  0x080002f5   Thumb Code    22  stm32f4xx_adc.o(i.ADC_Cmd)
    ADC_CommonInit                           0x0800030d   Thumb Code    34  stm32f4xx_adc.o(i.ADC_CommonInit)
    ADC_DMACmd                               0x0800033d   Thumb Code    22  stm32f4xx_adc.o(i.ADC_DMACmd)
    ADC_DMARequestAfterLastTransferCmd       0x08000353   Thumb Code    22  stm32f4xx_adc.o(i.ADC_DMARequestAfterLastTransferCmd)
    ADC_Init                                 0x08000369   Thumb Code    74  stm32f4xx_adc.o(i.ADC_Init)
    ADC_RegularChannelConfig                 0x080003bd   Thumb Code   184  stm32f4xx_adc.o(i.ADC_RegularChannelConfig)
    ARR_adjust                               0x08000475   Thumb Code   108  show.o(i.ARR_adjust)
    BusFault_Handler                         0x080004e5   Thumb Code     4  stm32f4xx_it.o(i.BusFault_Handler)
    DMA2_Stream0_IRQHandler                  0x080004e9   Thumb Code   520  main.o(i.DMA2_Stream0_IRQHandler)
    DMA_ClearITPendingBit                    0x0800074d   Thumb Code    38  stm32f4xx_dma.o(i.DMA_ClearITPendingBit)
    DMA_Cmd                                  0x08000781   Thumb Code    22  stm32f4xx_dma.o(i.DMA_Cmd)
    DMA_GetITStatus                          0x08000799   Thumb Code    82  stm32f4xx_dma.o(i.DMA_GetITStatus)
    DMA_ITConfig                             0x080007fd   Thumb Code    58  stm32f4xx_dma.o(i.DMA_ITConfig)
    DMA_Init                                 0x08000839   Thumb Code    82  stm32f4xx_dma.o(i.DMA_Init)
    DebugMon_Handler                         0x08000891   Thumb Code     2  stm32f4xx_it.o(i.DebugMon_Handler)
    GPIO_Init                                0x08000893   Thumb Code   144  stm32f4xx_gpio.o(i.GPIO_Init)
    GPIO_PinAFConfig                         0x08000923   Thumb Code    70  stm32f4xx_gpio.o(i.GPIO_PinAFConfig)
    GPIO_ReadInputDataBit                    0x08000969   Thumb Code    18  stm32f4xx_gpio.o(i.GPIO_ReadInputDataBit)
    GPIO_ResetBits                           0x0800097b   Thumb Code     4  stm32f4xx_gpio.o(i.GPIO_ResetBits)
    GPIO_SetBits                             0x0800097f   Thumb Code     4  stm32f4xx_gpio.o(i.GPIO_SetBits)
    GPIO_WriteBit                            0x08000983   Thumb Code    10  stm32f4xx_gpio.o(i.GPIO_WriteBit)
    HardFault_Handler                        0x0800098d   Thumb Code     4  stm32f4xx_it.o(i.HardFault_Handler)
    KEY_Calculate                            0x08000991   Thumb Code   662  key.o(i.KEY_Calculate)
    KEY_Init                                 0x08000c6d   Thumb Code    56  key.o(i.KEY_Init)
    KEY_Scan1                                0x08000cad   Thumb Code   178  key.o(i.KEY_Scan1)
    KEY_Scan3                                0x08000d69   Thumb Code    66  key.o(i.KEY_Scan3)
    LED_Init                                 0x08000db5   Thumb Code    88  led.o(i.LED_Init)
    MemManage_Handler                        0x08000e15   Thumb Code     4  stm32f4xx_it.o(i.MemManage_Handler)
    MyADC1_Init                              0x08000e19   Thumb Code   226  ad.o(i.MyADC1_Init)
    MyDMA1_Init                              0x08000f09   Thumb Code   166  ad.o(i.MyDMA1_Init)
    MyTIM3_Init                              0x08000fbd   Thumb Code    66  ad.o(i.MyTIM3_Init)
    NMI_Handler                              0x08001005   Thumb Code     2  stm32f4xx_it.o(i.NMI_Handler)
    NVIC_Init                                0x08001009   Thumb Code   106  misc.o(i.NVIC_Init)
    NVIC_PriorityGroupConfig                 0x08001081   Thumb Code    10  misc.o(i.NVIC_PriorityGroupConfig)
    OLED_Clear                               0x08001095   Thumb Code    42  oled.o(i.OLED_Clear)
    OLED_Float                               0x080010c1   Thumb Code   480  oled.o(i.OLED_Float)
    OLED_I2C_Init                            0x080012b1   Thumb Code    88  oled.o(i.OLED_I2C_Init)
    OLED_I2C_SendByte                        0x0800130d   Thumb Code   104  oled.o(i.OLED_I2C_SendByte)
    OLED_I2C_Start                           0x08001379   Thumb Code    60  oled.o(i.OLED_I2C_Start)
    OLED_I2C_Stop                            0x080013b9   Thumb Code    54  oled.o(i.OLED_I2C_Stop)
    OLED_Init                                0x080013f5   Thumb Code   158  oled.o(i.OLED_Init)
    OLED_SetCursor                           0x08001493   Thumb Code    34  oled.o(i.OLED_SetCursor)
    OLED_ShowChar                            0x080014b5   Thumb Code   102  oled.o(i.OLED_ShowChar)
    OLED_ShowString                          0x08001521   Thumb Code    40  oled.o(i.OLED_ShowString)
    OLED_WriteCommand                        0x08001549   Thumb Code    32  oled.o(i.OLED_WriteCommand)
    OLED_WriteData                           0x08001569   Thumb Code    32  oled.o(i.OLED_WriteData)
    PendSV_Handler                           0x08001589   Thumb Code     2  stm32f4xx_it.o(i.PendSV_Handler)
    RCC_AHB1PeriphClockCmd                   0x0800158d   Thumb Code    26  stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd)
    RCC_APB1PeriphClockCmd                   0x080015ad   Thumb Code    26  stm32f4xx_rcc.o(i.RCC_APB1PeriphClockCmd)
    RCC_APB2PeriphClockCmd                   0x080015cd   Thumb Code    26  stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd)
    RCC_APB2PeriphResetCmd                   0x080015ed   Thumb Code    26  stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd)
    RCC_GetClocksFreq                        0x0800160d   Thumb Code   214  stm32f4xx_rcc.o(i.RCC_GetClocksFreq)
    SVC_Handler                              0x080016f5   Thumb Code     2  stm32f4xx_it.o(i.SVC_Handler)
    SysTick_CLKSourceConfig                  0x080017e5   Thumb Code    40  misc.o(i.SysTick_CLKSourceConfig)
    SysTick_Handler                          0x0800180d   Thumb Code     2  stm32f4xx_it.o(i.SysTick_Handler)
    SystemInit                               0x08001811   Thumb Code    88  system_stm32f4xx.o(i.SystemInit)
    TIM1_PWM_CHN_Init                        0x08001879   Thumb Code   318  timer1.o(i.TIM1_PWM_CHN_Init)
    TIM8_PWM_CHN_Init                        0x080019c1   Thumb Code   342  timer1.o(i.TIM8_PWM_CHN_Init)
    TIM_ARRPreloadConfig                     0x08001b29   Thumb Code    24  stm32f4xx_tim.o(i.TIM_ARRPreloadConfig)
    TIM_BDTRConfig                           0x08001b41   Thumb Code    32  stm32f4xx_tim.o(i.TIM_BDTRConfig)
    TIM_Cmd                                  0x08001b61   Thumb Code    24  stm32f4xx_tim.o(i.TIM_Cmd)
    TIM_CtrlPWMOutputs                       0x08001b79   Thumb Code    30  stm32f4xx_tim.o(i.TIM_CtrlPWMOutputs)
    TIM_GenerateEvent                        0x08001b97   Thumb Code     4  stm32f4xx_tim.o(i.TIM_GenerateEvent)
    TIM_OC1Init                              0x08001b9d   Thumb Code   114  stm32f4xx_tim.o(i.TIM_OC1Init)
    TIM_OC1PreloadConfig                     0x08001c19   Thumb Code    18  stm32f4xx_tim.o(i.TIM_OC1PreloadConfig)
    TIM_OC2Init                              0x08001c2d   Thumb Code   154  stm32f4xx_tim.o(i.TIM_OC2Init)
    TIM_OC2PreloadConfig                     0x08001cd1   Thumb Code    26  stm32f4xx_tim.o(i.TIM_OC2PreloadConfig)
    TIM_OC3Init                              0x08001ced   Thumb Code   150  stm32f4xx_tim.o(i.TIM_OC3Init)
    TIM_OC3PreloadConfig                     0x08001d8d   Thumb Code    18  stm32f4xx_tim.o(i.TIM_OC3PreloadConfig)
    TIM_SelectOutputTrigger                  0x08001d9f   Thumb Code    18  stm32f4xx_tim.o(i.TIM_SelectOutputTrigger)
    TIM_SetAutoreload                        0x08001db1   Thumb Code     4  stm32f4xx_tim.o(i.TIM_SetAutoreload)
    TIM_TimeBaseInit                         0x08001db5   Thumb Code   104  stm32f4xx_tim.o(i.TIM_TimeBaseInit)
    USART1_IRQHandler                        0x08001e39   Thumb Code   122  usart.o(i.USART1_IRQHandler)
    USART_Cmd                                0x08001ec1   Thumb Code    24  stm32f4xx_usart.o(i.USART_Cmd)
    USART_GetITStatus                        0x08001ed9   Thumb Code    84  stm32f4xx_usart.o(i.USART_GetITStatus)
    USART_ITConfig                           0x08001f2d   Thumb Code    74  stm32f4xx_usart.o(i.USART_ITConfig)
    USART_Init                               0x08001f79   Thumb Code   204  stm32f4xx_usart.o(i.USART_Init)
    USART_ReceiveData                        0x0800204d   Thumb Code    10  stm32f4xx_usart.o(i.USART_ReceiveData)
    UsageFault_Handler                       0x08002057   Thumb Code     4  stm32f4xx_it.o(i.UsageFault_Handler)
    __ARM_fpclassify                         0x0800205b   Thumb Code    48  fpclassify.o(i.__ARM_fpclassify)
    __hardfp_sin                             0x08002091   Thumb Code   180  sin.o(i.__hardfp_sin)
    __ieee754_rem_pio2                       0x08002159   Thumb Code   938  rred.o(i.__ieee754_rem_pio2)
    __kernel_cos                             0x08002591   Thumb Code   322  cos_i.o(i.__kernel_cos)
    __kernel_poly                            0x08002701   Thumb Code   248  poly.o(i.__kernel_poly)
    __kernel_sin                             0x080027f9   Thumb Code   280  sin_i.o(i.__kernel_sin)
    __mathlib_dbl_infnan                     0x08002929   Thumb Code    20  dunder.o(i.__mathlib_dbl_infnan)
    __mathlib_dbl_invalid                    0x08002941   Thumb Code    24  dunder.o(i.__mathlib_dbl_invalid)
    __mathlib_dbl_underflow                  0x08002961   Thumb Code    24  dunder.o(i.__mathlib_dbl_underflow)
    _sys_exit                                0x08002981   Thumb Code     4  usart.o(i._sys_exit)
    delay_init                               0x08002985   Thumb Code    52  delay.o(i.delay_init)
    delay_ms                                 0x080029c1   Thumb Code    56  delay.o(i.delay_ms)
    delay_us                                 0x080029f9   Thumb Code    72  delay.o(i.delay_us)
    delay_xms                                0x08002a45   Thumb Code    72  delay.o(i.delay_xms)
    fabs                                     0x08002a91   Thumb Code    24  fabs.o(i.fabs)
    get_sin_tab1                             0x08002aa9   Thumb Code   140  sine.o(i.get_sin_tab1)
    invt_init                                0x08002b41   Thumb Code   174  inveter.o(i.invt_init)
    invt_loop                                0x08002bf5   Thumb Code   398  inveter.o(i.invt_loop)
    main                                     0x08002da5   Thumb Code   288  main.o(i.main)
    pfc_init                                 0x08002ef1   Thumb Code   210  pfc.o(i.pfc_init)
    show2                                    0x08002fd5   Thumb Code    90  show.o(i.show2)
    show_AD                                  0x08003035   Thumb Code    44  show.o(i.show_AD)
    uart_init                                0x08003081   Thumb Code   166  usart.o(i.uart_init)
    update_TIM_ARR                           0x08003131   Thumb Code    46  show.o(i.update_TIM_ARR)
    __aeabi_dneg                             0x08003165   Thumb Code     0  basic.o(x$fpl$basic)
    _dneg                                    0x08003165   Thumb Code     6  basic.o(x$fpl$basic)
    __aeabi_fneg                             0x0800316b   Thumb Code     0  basic.o(x$fpl$basic)
    _fneg                                    0x0800316b   Thumb Code     6  basic.o(x$fpl$basic)
    _dabs                                    0x08003171   Thumb Code     6  basic.o(x$fpl$basic)
    _fabs                                    0x08003177   Thumb Code     6  basic.o(x$fpl$basic)
    __aeabi_d2f                              0x0800317d   Thumb Code     0  d2f.o(x$fpl$d2f)
    _d2f                                     0x0800317d   Thumb Code    98  d2f.o(x$fpl$d2f)
    __aeabi_dadd                             0x080031e1   Thumb Code     0  daddsub_clz.o(x$fpl$dadd)
    _dadd                                    0x080031e1   Thumb Code   332  daddsub_clz.o(x$fpl$dadd)
    __fpl_dcmp_Inf                           0x08003331   Thumb Code    24  dcmpi.o(x$fpl$dcmpinf)
    __aeabi_ddiv                             0x08003349   Thumb Code     0  ddiv.o(x$fpl$ddiv)
    _ddiv                                    0x08003349   Thumb Code   552  ddiv.o(x$fpl$ddiv)
    __aeabi_d2iz                             0x080035f9   Thumb Code     0  dfix.o(x$fpl$dfix)
    _dfix                                    0x080035f9   Thumb Code    94  dfix.o(x$fpl$dfix)
    __aeabi_i2d                              0x08003657   Thumb Code     0  dflt_clz.o(x$fpl$dflt)
    _dflt                                    0x08003657   Thumb Code    46  dflt_clz.o(x$fpl$dflt)
    __aeabi_ui2d                             0x08003685   Thumb Code     0  dflt_clz.o(x$fpl$dfltu)
    _dfltu                                   0x08003685   Thumb Code    38  dflt_clz.o(x$fpl$dfltu)
    __aeabi_cdcmple                          0x080036ad   Thumb Code     0  dleqf.o(x$fpl$dleqf)
    _dcmple                                  0x080036ad   Thumb Code   120  dleqf.o(x$fpl$dleqf)
    __fpl_dcmple_InfNaN                      0x0800370f   Thumb Code     0  dleqf.o(x$fpl$dleqf)
    __aeabi_dmul                             0x08003725   Thumb Code     0  dmul.o(x$fpl$dmul)
    _dmul                                    0x08003725   Thumb Code   332  dmul.o(x$fpl$dmul)
    __fpl_dnaninf                            0x08003879   Thumb Code   156  dnaninf.o(x$fpl$dnaninf)
    __fpl_dretinf                            0x08003915   Thumb Code    12  dretinf.o(x$fpl$dretinf)
    __aeabi_drsub                            0x08003921   Thumb Code     0  daddsub_clz.o(x$fpl$drsb)
    _drsb                                    0x08003921   Thumb Code    22  daddsub_clz.o(x$fpl$drsb)
    __aeabi_dsub                             0x08003939   Thumb Code     0  daddsub_clz.o(x$fpl$dsub)
    _dsub                                    0x08003939   Thumb Code   464  daddsub_clz.o(x$fpl$dsub)
    __aeabi_f2d                              0x08003b0d   Thumb Code     0  f2d.o(x$fpl$f2d)
    _f2d                                     0x08003b0d   Thumb Code    86  f2d.o(x$fpl$f2d)
    __fpl_fnaninf                            0x08003b63   Thumb Code   140  fnaninf.o(x$fpl$fnaninf)
    _fp_init                                 0x08003bef   Thumb Code    10  fpinit.o(x$fpl$fpinit)
    __fplib_config_fpu_vfp                   0x08003bf7   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    __fplib_config_pureend_doubles           0x08003bf7   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    __fpl_fretinf                            0x08003bf9   Thumb Code    10  fretinf.o(x$fpl$fretinf)
    OLED_F8x16                               0x08003c02   Data        1520  oled.o(.constdata)
    __I$use$fp                               0x08003c02   Number         0  usenofp.o(x$fpl$usenofp)
    ARR_Table                                0x080041f2   Data         162  show.o(.constdata)
    Region$$Table$$Base                      0x080043b8   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x080043d8   Number         0  anon$$obj.o(Region$$Table)
    switch_                                  0x20000000   Data           4  main.o(.data)
    flag                                     0x20000004   Data           4  main.o(.data)
    flag2                                    0x20000008   Data           4  main.o(.data)
    i                                        0x2000000c   Data           4  main.o(.data)
    data                                     0x20000010   Data           4  main.o(.data)
    x                                        0x20000014   Data           4  main.o(.data)
    AD_Value                                 0x20000030   Data           8  ad.o(.data)
    ADC_Value                                0x20000038   Data           8  ad.o(.data)
    __stdout                                 0x20000068   Data           4  usart.o(.data)
    USART_RX_STA                             0x2000006c   Data           2  usart.o(.data)
    udc                                      0x20000080   Data          40  main.o(.bss)
    pfc                                      0x200000a8   Data         176  main.o(.bss)
    invt                                     0x20000158   Data         116  main.o(.bss)
    sinData                                  0x200001cc   Data        1600  sine.o(.bss)
    USART_RX_BUF                             0x2000080c   Data         200  usart.o(.bss)
    __libspace_start                         0x200008d4   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x20000934   Data           0  libspace.o(.bss)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000189

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00004458, Max: 0x00100000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x000043d8, Max: 0x00100000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000188   Data   RO         1131    RESET               startup_stm32f40_41xxx.o
    0x08000188   0x08000188   0x00000008   Code   RO         2994  * !!!main             c_w.l(__main.o)
    0x08000190   0x08000190   0x00000034   Code   RO         3247    !!!scatter          c_w.l(__scatter.o)
    0x080001c4   0x080001c4   0x0000001a   Code   RO         3249    !!handler_copy      c_w.l(__scatter_copy.o)
    0x080001de   0x080001de   0x00000002   PAD
    0x080001e0   0x080001e0   0x0000001c   Code   RO         3251    !!handler_zi        c_w.l(__scatter_zi.o)
    0x080001fc   0x080001fc   0x00000002   Code   RO         3124    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x080001fe   0x080001fe   0x00000004   Code   RO         3127    .ARM.Collect$$libinit$$00000001  c_w.l(libinit2.o)
    0x08000202   0x08000202   0x00000000   Code   RO         3130    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x08000202   0x08000202   0x00000000   Code   RO         3133    .ARM.Collect$$libinit$$0000000A  c_w.l(libinit2.o)
    0x08000202   0x08000202   0x00000000   Code   RO         3135    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x08000202   0x08000202   0x00000000   Code   RO         3137    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x08000202   0x08000202   0x00000000   Code   RO         3140    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x08000202   0x08000202   0x00000000   Code   RO         3142    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x08000202   0x08000202   0x00000000   Code   RO         3144    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x08000202   0x08000202   0x00000000   Code   RO         3146    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x08000202   0x08000202   0x00000000   Code   RO         3148    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x08000202   0x08000202   0x00000000   Code   RO         3150    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x08000202   0x08000202   0x00000000   Code   RO         3152    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x08000202   0x08000202   0x00000000   Code   RO         3154    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x08000202   0x08000202   0x00000000   Code   RO         3156    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x08000202   0x08000202   0x00000000   Code   RO         3158    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x08000202   0x08000202   0x00000000   Code   RO         3160    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x08000202   0x08000202   0x00000000   Code   RO         3164    .ARM.Collect$$libinit$$0000002C  c_w.l(libinit2.o)
    0x08000202   0x08000202   0x00000000   Code   RO         3166    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x08000202   0x08000202   0x00000000   Code   RO         3168    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x08000202   0x08000202   0x00000000   Code   RO         3170    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x08000202   0x08000202   0x00000002   Code   RO         3171    .ARM.Collect$$libinit$$00000033  c_w.l(libinit2.o)
    0x08000204   0x08000204   0x00000002   Code   RO         3189    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x08000206   0x08000206   0x00000000   Code   RO         3199    .ARM.Collect$$libshutdown$$00000002  c_w.l(libshutdown2.o)
    0x08000206   0x08000206   0x00000000   Code   RO         3201    .ARM.Collect$$libshutdown$$00000004  c_w.l(libshutdown2.o)
    0x08000206   0x08000206   0x00000000   Code   RO         3204    .ARM.Collect$$libshutdown$$00000007  c_w.l(libshutdown2.o)
    0x08000206   0x08000206   0x00000000   Code   RO         3207    .ARM.Collect$$libshutdown$$0000000A  c_w.l(libshutdown2.o)
    0x08000206   0x08000206   0x00000000   Code   RO         3209    .ARM.Collect$$libshutdown$$0000000C  c_w.l(libshutdown2.o)
    0x08000206   0x08000206   0x00000000   Code   RO         3212    .ARM.Collect$$libshutdown$$0000000F  c_w.l(libshutdown2.o)
    0x08000206   0x08000206   0x00000002   Code   RO         3213    .ARM.Collect$$libshutdown$$00000010  c_w.l(libshutdown2.o)
    0x08000208   0x08000208   0x00000000   Code   RO         3036    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x08000208   0x08000208   0x00000000   Code   RO         3079    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x08000208   0x08000208   0x00000006   Code   RO         3091    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x0800020e   0x0800020e   0x00000000   Code   RO         3081    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x0800020e   0x0800020e   0x00000004   Code   RO         3082    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x08000212   0x08000212   0x00000000   Code   RO         3084    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x08000212   0x08000212   0x00000008   Code   RO         3085    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x0800021a   0x0800021a   0x00000002   Code   RO         3125    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x0800021c   0x0800021c   0x00000000   Code   RO         3173    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x0800021c   0x0800021c   0x00000004   Code   RO         3174    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x08000220   0x08000220   0x00000006   Code   RO         3175    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x08000226   0x08000226   0x00000002   PAD
    0x08000228   0x08000228   0x00000040   Code   RO         1132    .text               startup_stm32f40_41xxx.o
    0x08000268   0x08000268   0x00000002   Code   RO         2990    .text               c_w.l(use_no_semi_2.o)
    0x0800026a   0x0800026a   0x00000006   Code   RO         2992    .text               c_w.l(heapauxi.o)
    0x08000270   0x08000270   0x00000002   Code   RO         3034    .text               c_w.l(use_no_semi.o)
    0x08000272   0x08000272   0x00000016   Code   RO         3037    .text               c_w.l(_rserrno.o)
    0x08000288   0x08000288   0x00000008   Code   RO         3096    .text               c_w.l(rt_errno_addr_intlibspace.o)
    0x08000290   0x08000290   0x00000008   Code   RO         3112    .text               c_w.l(libspace.o)
    0x08000298   0x08000298   0x0000004a   Code   RO         3115    .text               c_w.l(sys_stackheap_outer.o)
    0x080002e2   0x080002e2   0x00000012   Code   RO         3117    .text               c_w.l(exit.o)
    0x080002f4   0x080002f4   0x00000016   Code   RO         1864    i.ADC_Cmd           stm32f4xx_adc.o
    0x0800030a   0x0800030a   0x00000002   PAD
    0x0800030c   0x0800030c   0x00000030   Code   RO         1865    i.ADC_CommonInit    stm32f4xx_adc.o
    0x0800033c   0x0800033c   0x00000016   Code   RO         1868    i.ADC_DMACmd        stm32f4xx_adc.o
    0x08000352   0x08000352   0x00000016   Code   RO         1869    i.ADC_DMARequestAfterLastTransferCmd  stm32f4xx_adc.o
    0x08000368   0x08000368   0x00000054   Code   RO         1884    i.ADC_Init          stm32f4xx_adc.o
    0x080003bc   0x080003bc   0x000000b8   Code   RO         1889    i.ADC_RegularChannelConfig  stm32f4xx_adc.o
    0x08000474   0x08000474   0x00000070   Code   RO          880    i.ARR_adjust        show.o
    0x080004e4   0x080004e4   0x00000004   Code   RO          188    i.BusFault_Handler  stm32f4xx_it.o
    0x080004e8   0x080004e8   0x00000264   Code   RO            3    i.DMA2_Stream0_IRQHandler  main.o
    0x0800074c   0x0800074c   0x00000034   Code   RO         2101    i.DMA_ClearITPendingBit  stm32f4xx_dma.o
    0x08000780   0x08000780   0x00000016   Code   RO         2102    i.DMA_Cmd           stm32f4xx_dma.o
    0x08000796   0x08000796   0x00000002   PAD
    0x08000798   0x08000798   0x00000064   Code   RO         2112    i.DMA_GetITStatus   stm32f4xx_dma.o
    0x080007fc   0x080007fc   0x0000003a   Code   RO         2113    i.DMA_ITConfig      stm32f4xx_dma.o
    0x08000836   0x08000836   0x00000002   PAD
    0x08000838   0x08000838   0x00000058   Code   RO         2114    i.DMA_Init          stm32f4xx_dma.o
    0x08000890   0x08000890   0x00000002   Code   RO          189    i.DebugMon_Handler  stm32f4xx_it.o
    0x08000892   0x08000892   0x00000090   Code   RO         1183    i.GPIO_Init         stm32f4xx_gpio.o
    0x08000922   0x08000922   0x00000046   Code   RO         1184    i.GPIO_PinAFConfig  stm32f4xx_gpio.o
    0x08000968   0x08000968   0x00000012   Code   RO         1187    i.GPIO_ReadInputDataBit  stm32f4xx_gpio.o
    0x0800097a   0x0800097a   0x00000004   Code   RO         1190    i.GPIO_ResetBits    stm32f4xx_gpio.o
    0x0800097e   0x0800097e   0x00000004   Code   RO         1191    i.GPIO_SetBits      stm32f4xx_gpio.o
    0x08000982   0x08000982   0x0000000a   Code   RO         1195    i.GPIO_WriteBit     stm32f4xx_gpio.o
    0x0800098c   0x0800098c   0x00000004   Code   RO          190    i.HardFault_Handler  stm32f4xx_it.o
    0x08000990   0x08000990   0x000002dc   Code   RO          955    i.KEY_Calculate     key.o
    0x08000c6c   0x08000c6c   0x00000040   Code   RO          956    i.KEY_Init          key.o
    0x08000cac   0x08000cac   0x000000bc   Code   RO          957    i.KEY_Scan1         key.o
    0x08000d68   0x08000d68   0x0000004c   Code   RO          958    i.KEY_Scan3         key.o
    0x08000db4   0x08000db4   0x00000060   Code   RO          397    i.LED_Init          led.o
    0x08000e14   0x08000e14   0x00000004   Code   RO          191    i.MemManage_Handler  stm32f4xx_it.o
    0x08000e18   0x08000e18   0x000000f0   Code   RO          521    i.MyADC1_Init       ad.o
    0x08000f08   0x08000f08   0x000000b4   Code   RO          522    i.MyDMA1_Init       ad.o
    0x08000fbc   0x08000fbc   0x00000048   Code   RO          524    i.MyTIM3_Init       ad.o
    0x08001004   0x08001004   0x00000002   Code   RO          192    i.NMI_Handler       stm32f4xx_it.o
    0x08001006   0x08001006   0x00000002   PAD
    0x08001008   0x08001008   0x00000078   Code   RO         1138    i.NVIC_Init         misc.o
    0x08001080   0x08001080   0x00000014   Code   RO         1139    i.NVIC_PriorityGroupConfig  misc.o
    0x08001094   0x08001094   0x0000002a   Code   RO          565    i.OLED_Clear        oled.o
    0x080010be   0x080010be   0x00000002   PAD
    0x080010c0   0x080010c0   0x000001f0   Code   RO          566    i.OLED_Float        oled.o
    0x080012b0   0x080012b0   0x0000005c   Code   RO          567    i.OLED_I2C_Init     oled.o
    0x0800130c   0x0800130c   0x0000006c   Code   RO          568    i.OLED_I2C_SendByte  oled.o
    0x08001378   0x08001378   0x00000040   Code   RO          569    i.OLED_I2C_Start    oled.o
    0x080013b8   0x080013b8   0x0000003c   Code   RO          570    i.OLED_I2C_Stop     oled.o
    0x080013f4   0x080013f4   0x0000009e   Code   RO          571    i.OLED_Init         oled.o
    0x08001492   0x08001492   0x00000022   Code   RO          573    i.OLED_SetCursor    oled.o
    0x080014b4   0x080014b4   0x0000006c   Code   RO          575    i.OLED_ShowChar     oled.o
    0x08001520   0x08001520   0x00000028   Code   RO          579    i.OLED_ShowString   oled.o
    0x08001548   0x08001548   0x00000020   Code   RO          580    i.OLED_WriteCommand  oled.o
    0x08001568   0x08001568   0x00000020   Code   RO          581    i.OLED_WriteData    oled.o
    0x08001588   0x08001588   0x00000002   Code   RO          193    i.PendSV_Handler    stm32f4xx_it.o
    0x0800158a   0x0800158a   0x00000002   PAD
    0x0800158c   0x0800158c   0x00000020   Code   RO         1280    i.RCC_AHB1PeriphClockCmd  stm32f4xx_rcc.o
    0x080015ac   0x080015ac   0x00000020   Code   RO         1289    i.RCC_APB1PeriphClockCmd  stm32f4xx_rcc.o
    0x080015cc   0x080015cc   0x00000020   Code   RO         1292    i.RCC_APB2PeriphClockCmd  stm32f4xx_rcc.o
    0x080015ec   0x080015ec   0x00000020   Code   RO         1294    i.RCC_APB2PeriphResetCmd  stm32f4xx_rcc.o
    0x0800160c   0x0800160c   0x000000e8   Code   RO         1301    i.RCC_GetClocksFreq  stm32f4xx_rcc.o
    0x080016f4   0x080016f4   0x00000002   Code   RO          194    i.SVC_Handler       stm32f4xx_it.o
    0x080016f6   0x080016f6   0x00000002   PAD
    0x080016f8   0x080016f8   0x000000ec   Code   RO          360    i.SetSysClock       system_stm32f4xx.o
    0x080017e4   0x080017e4   0x00000028   Code   RO         1142    i.SysTick_CLKSourceConfig  misc.o
    0x0800180c   0x0800180c   0x00000002   Code   RO          195    i.SysTick_Handler   stm32f4xx_it.o
    0x0800180e   0x0800180e   0x00000002   PAD
    0x08001810   0x08001810   0x00000068   Code   RO          362    i.SystemInit        system_stm32f4xx.o
    0x08001878   0x08001878   0x00000148   Code   RO          851    i.TIM1_PWM_CHN_Init  timer1.o
    0x080019c0   0x080019c0   0x00000168   Code   RO          852    i.TIM8_PWM_CHN_Init  timer1.o
    0x08001b28   0x08001b28   0x00000018   Code   RO         2232    i.TIM_ARRPreloadConfig  stm32f4xx_tim.o
    0x08001b40   0x08001b40   0x00000020   Code   RO         2233    i.TIM_BDTRConfig    stm32f4xx_tim.o
    0x08001b60   0x08001b60   0x00000018   Code   RO         2244    i.TIM_Cmd           stm32f4xx_tim.o
    0x08001b78   0x08001b78   0x0000001e   Code   RO         2246    i.TIM_CtrlPWMOutputs  stm32f4xx_tim.o
    0x08001b96   0x08001b96   0x00000004   Code   RO         2258    i.TIM_GenerateEvent  stm32f4xx_tim.o
    0x08001b9a   0x08001b9a   0x00000002   PAD
    0x08001b9c   0x08001b9c   0x0000007c   Code   RO         2273    i.TIM_OC1Init       stm32f4xx_tim.o
    0x08001c18   0x08001c18   0x00000012   Code   RO         2276    i.TIM_OC1PreloadConfig  stm32f4xx_tim.o
    0x08001c2a   0x08001c2a   0x00000002   PAD
    0x08001c2c   0x08001c2c   0x000000a4   Code   RO         2278    i.TIM_OC2Init       stm32f4xx_tim.o
    0x08001cd0   0x08001cd0   0x0000001a   Code   RO         2281    i.TIM_OC2PreloadConfig  stm32f4xx_tim.o
    0x08001cea   0x08001cea   0x00000002   PAD
    0x08001cec   0x08001cec   0x000000a0   Code   RO         2283    i.TIM_OC3Init       stm32f4xx_tim.o
    0x08001d8c   0x08001d8c   0x00000012   Code   RO         2286    i.TIM_OC3PreloadConfig  stm32f4xx_tim.o
    0x08001d9e   0x08001d9e   0x00000012   Code   RO         2302    i.TIM_SelectOutputTrigger  stm32f4xx_tim.o
    0x08001db0   0x08001db0   0x00000004   Code   RO         2304    i.TIM_SetAutoreload  stm32f4xx_tim.o
    0x08001db4   0x08001db4   0x00000084   Code   RO         2316    i.TIM_TimeBaseInit  stm32f4xx_tim.o
    0x08001e38   0x08001e38   0x00000088   Code   RO         1082    i.USART1_IRQHandler  usart.o
    0x08001ec0   0x08001ec0   0x00000018   Code   RO         1674    i.USART_Cmd         stm32f4xx_usart.o
    0x08001ed8   0x08001ed8   0x00000054   Code   RO         1678    i.USART_GetITStatus  stm32f4xx_usart.o
    0x08001f2c   0x08001f2c   0x0000004a   Code   RO         1680    i.USART_ITConfig    stm32f4xx_usart.o
    0x08001f76   0x08001f76   0x00000002   PAD
    0x08001f78   0x08001f78   0x000000d4   Code   RO         1681    i.USART_Init        stm32f4xx_usart.o
    0x0800204c   0x0800204c   0x0000000a   Code   RO         1688    i.USART_ReceiveData  stm32f4xx_usart.o
    0x08002056   0x08002056   0x00000004   Code   RO          196    i.UsageFault_Handler  stm32f4xx_it.o
    0x0800205a   0x0800205a   0x00000030   Code   RO         3108    i.__ARM_fpclassify  m_wm.l(fpclassify.o)
    0x0800208a   0x0800208a   0x00000006   PAD
    0x08002090   0x08002090   0x000000c8   Code   RO         3022    i.__hardfp_sin      m_wm.l(sin.o)
    0x08002158   0x08002158   0x00000438   Code   RO         3067    i.__ieee754_rem_pio2  m_wm.l(rred.o)
    0x08002590   0x08002590   0x00000170   Code   RO         3050    i.__kernel_cos      m_wm.l(cos_i.o)
    0x08002700   0x08002700   0x000000f8   Code   RO         3110    i.__kernel_poly     m_wm.l(poly.o)
    0x080027f8   0x080027f8   0x00000130   Code   RO         3072    i.__kernel_sin      m_wm.l(sin_i.o)
    0x08002928   0x08002928   0x00000014   Code   RO         3054    i.__mathlib_dbl_infnan  m_wm.l(dunder.o)
    0x0800293c   0x0800293c   0x00000004   PAD
    0x08002940   0x08002940   0x00000020   Code   RO         3056    i.__mathlib_dbl_invalid  m_wm.l(dunder.o)
    0x08002960   0x08002960   0x00000020   Code   RO         3059    i.__mathlib_dbl_underflow  m_wm.l(dunder.o)
    0x08002980   0x08002980   0x00000004   Code   RO         1083    i._sys_exit         usart.o
    0x08002984   0x08002984   0x0000003c   Code   RO         1021    i.delay_init        delay.o
    0x080029c0   0x080029c0   0x00000038   Code   RO         1022    i.delay_ms          delay.o
    0x080029f8   0x080029f8   0x0000004c   Code   RO         1023    i.delay_us          delay.o
    0x08002a44   0x08002a44   0x0000004c   Code   RO         1024    i.delay_xms         delay.o
    0x08002a90   0x08002a90   0x00000018   Code   RO         3104    i.fabs              m_wm.l(fabs.o)
    0x08002aa8   0x08002aa8   0x00000098   Code   RO          449    i.get_sin_tab1      sine.o
    0x08002b40   0x08002b40   0x000000b4   Code   RO          994    i.invt_init         inveter.o
    0x08002bf4   0x08002bf4   0x000001b0   Code   RO          995    i.invt_loop         inveter.o
    0x08002da4   0x08002da4   0x0000014c   Code   RO            4    i.main              main.o
    0x08002ef0   0x08002ef0   0x000000e4   Code   RO          797    i.pfc_init          pfc.o
    0x08002fd4   0x08002fd4   0x00000060   Code   RO          882    i.show2             show.o
    0x08003034   0x08003034   0x0000004c   Code   RO          883    i.show_AD           show.o
    0x08003080   0x08003080   0x000000b0   Code   RO         1085    i.uart_init         usart.o
    0x08003130   0x08003130   0x00000034   Code   RO          886    i.update_TIM_ARR    show.o
    0x08003164   0x08003164   0x00000018   Code   RO         2996    x$fpl$basic         fz_wm.l(basic.o)
    0x0800317c   0x0800317c   0x00000062   Code   RO         2998    x$fpl$d2f           fz_wm.l(d2f.o)
    0x080031de   0x080031de   0x00000002   PAD
    0x080031e0   0x080031e0   0x00000150   Code   RO         3000    x$fpl$dadd          fz_wm.l(daddsub_clz.o)
    0x08003330   0x08003330   0x00000018   Code   RO         3039    x$fpl$dcmpinf       fz_wm.l(dcmpi.o)
    0x08003348   0x08003348   0x000002b0   Code   RO         3099    x$fpl$ddiv          fz_wm.l(ddiv.o)
    0x080035f8   0x080035f8   0x0000005e   Code   RO         3006    x$fpl$dfix          fz_wm.l(dfix.o)
    0x08003656   0x08003656   0x0000002e   Code   RO         3011    x$fpl$dflt          fz_wm.l(dflt_clz.o)
    0x08003684   0x08003684   0x00000026   Code   RO         3010    x$fpl$dfltu         fz_wm.l(dflt_clz.o)
    0x080036aa   0x080036aa   0x00000002   PAD
    0x080036ac   0x080036ac   0x00000078   Code   RO         3016    x$fpl$dleqf         fz_wm.l(dleqf.o)
    0x08003724   0x08003724   0x00000154   Code   RO         3018    x$fpl$dmul          fz_wm.l(dmul.o)
    0x08003878   0x08003878   0x0000009c   Code   RO         3041    x$fpl$dnaninf       fz_wm.l(dnaninf.o)
    0x08003914   0x08003914   0x0000000c   Code   RO         3043    x$fpl$dretinf       fz_wm.l(dretinf.o)
    0x08003920   0x08003920   0x00000016   Code   RO         3001    x$fpl$drsb          fz_wm.l(daddsub_clz.o)
    0x08003936   0x08003936   0x00000002   PAD
    0x08003938   0x08003938   0x000001d4   Code   RO         3002    x$fpl$dsub          fz_wm.l(daddsub_clz.o)
    0x08003b0c   0x08003b0c   0x00000056   Code   RO         3020    x$fpl$f2d           fz_wm.l(f2d.o)
    0x08003b62   0x08003b62   0x0000008c   Code   RO         3045    x$fpl$fnaninf       fz_wm.l(fnaninf.o)
    0x08003bee   0x08003bee   0x0000000a   Code   RO         3181    x$fpl$fpinit        fz_wm.l(fpinit.o)
    0x08003bf8   0x08003bf8   0x0000000a   Code   RO         3047    x$fpl$fretinf       fz_wm.l(fretinf.o)
    0x08003c02   0x08003c02   0x00000000   Code   RO         3049    x$fpl$usenofp       fz_wm.l(usenofp.o)
    0x08003c02   0x08003c02   0x000005f0   Data   RO          582    .constdata          oled.o
    0x080041f2   0x080041f2   0x000000a2   Data   RO          888    .constdata          show.o
    0x08004294   0x08004294   0x00000004   PAD
    0x08004298   0x08004298   0x00000030   Data   RO         3051    .constdata          m_wm.l(cos_i.o)
    0x080042c8   0x080042c8   0x000000c8   Data   RO         3069    .constdata          m_wm.l(rred.o)
    0x08004390   0x08004390   0x00000028   Data   RO         3073    .constdata          m_wm.l(sin_i.o)
    0x080043b8   0x080043b8   0x00000020   Data   RO         3245    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM2 (Exec base: 0x10000000, Load base: 0x08004458, Size: 0x00000000, Max: 0x00010000, ABSOLUTE)

    **** No section assigned to this execution region ****


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x080043d8, Size: 0x00000f38, Max: 0x00020000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x080043d8   0x00000030   Data   RW            6    .data               main.o
    0x20000030   0x08004408   0x00000010   Data   RW          525    .data               ad.o
    0x20000040   0x08004418   0x00000024   Data   RW          959    .data               key.o
    0x20000064   0x0800443c   0x00000004   Data   RW         1025    .data               delay.o
    0x20000068   0x08004440   0x00000006   Data   RW         1087    .data               usart.o
    0x2000006e   0x08004446   0x00000010   Data   RW         1333    .data               stm32f4xx_rcc.o
    0x2000007e   0x08004456   0x00000002   PAD
    0x20000080        -       0x0000014c   Zero   RW            5    .bss                main.o
    0x200001cc        -       0x00000640   Zero   RW          450    .bss                sine.o
    0x2000080c        -       0x000000c8   Zero   RW         1086    .bss                usart.o
    0x200008d4        -       0x00000060   Zero   RW         3113    .bss                c_w.l(libspace.o)
    0x20000934   0x08004456   0x00000004   PAD
    0x20000938        -       0x00000200   Zero   RW         1130    HEAP                startup_stm32f40_41xxx.o
    0x20000b38        -       0x00000400   Zero   RW         1129    STACK               startup_stm32f40_41xxx.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       492         34          0         16          0       2855   ad.o
       268         16          0          4          0       2771   delay.o
       612         40          0          0          0       1784   inveter.o
      1060         98          0         36          0       3179   key.o
        96          8          0          0          0     243767   led.o
       944        136          0         48        332     243712   main.o
       180         24          0          0          0       2273   misc.o
      1266         40       1520          0          0     262868   oled.o
       228         18          0          0          0       2611   pfc.o
       336         48        162          0          0       2988   show.o
       152         12          0          0       1600       1461   sine.o
        64         26        392          0       1536        872   startup_stm32f40_41xxx.o
       382         24          0          0          0       5319   stm32f4xx_adc.o
       320         38          0          0          0       4734   stm32f4xx_dma.o
       250          0          0          0          0       4463   stm32f4xx_gpio.o
        26          0          0          0          0      65218   stm32f4xx_it.o
       360         42          0         16          0       5560   stm32f4xx_rcc.o
       778         58          0          0          0      10011   stm32f4xx_tim.o
       404          8          0          0          0       4550   stm32f4xx_usart.o
       340         32          0          0          0       1621   system_stm32f4xx.o
       688         28          0          0          0       1972   timer1.o
       316         24          0          6        200       3976   usart.o

    ----------------------------------------------------------------------
      9586        <USER>       <GROUP>        128       3668     878565   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        24          0          4          2          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         8          0          0          0          0         68   __main.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        52          8          0          0          0          0   __scatter.o
        26          0          0          0          0          0   __scatter_copy.o
        28          0          0          0          0          0   __scatter_zi.o
        22          0          0          0          0        100   _rserrno.o
        18          0          0          0          0         80   exit.o
         6          0          0          0          0        152   heapauxi.o
         2          0          0          0          0          0   libinit.o
         6          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
         8          4          0          0          0         68   rt_errno_addr_intlibspace.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
        74          0          0          0          0         80   sys_stackheap_outer.o
         2          0          0          0          0         68   use_no_semi.o
         2          0          0          0          0         68   use_no_semi_2.o
        24          0          0          0          0        164   basic.o
        98          4          0          0          0        140   d2f.o
       826         16          0          0          0        492   daddsub_clz.o
        24          0          0          0          0        116   dcmpi.o
       688        140          0          0          0        256   ddiv.o
        94          4          0          0          0        140   dfix.o
        84          0          0          0          0        232   dflt_clz.o
       120          4          0          0          0        140   dleqf.o
       340         12          0          0          0        152   dmul.o
       156          4          0          0          0        140   dnaninf.o
        12          0          0          0          0        116   dretinf.o
        86          4          0          0          0        132   f2d.o
       140          4          0          0          0        132   fnaninf.o
        10          0          0          0          0        116   fpinit.o
        10          0          0          0          0        116   fretinf.o
         0          0          0          0          0          0   usenofp.o
       368         46         48          0          0        200   cos_i.o
        84         16          0          0          0        372   dunder.o
        24          0          0          0          0        124   fabs.o
        48          0          0          0          0        124   fpclassify.o
       248          0          0          0          0        152   poly.o
      1080        142        200          0          0        188   rred.o
       200         20          0          0          0        164   sin.o
       304         24         40          0          0        208   sin_i.o

    ----------------------------------------------------------------------
      5384        <USER>        <GROUP>          0        100       4868   Library Totals
        20          0          0          0          4          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

       296         16          0          0         96        752   c_w.l
      2712        192          0          0          0       2584   fz_wm.l
      2356        248        288          0          0       1532   m_wm.l

    ----------------------------------------------------------------------
      5384        <USER>        <GROUP>          0        100       4868   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     14970       1210       2398        128       3768     871973   Grand Totals
     14970       1210       2398        128       3768     871973   ELF Image Totals
     14970       1210       2398        128          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                17368 (  16.96kB)
    Total RW  Size (RW Data + ZI Data)              3896 (   3.80kB)
    Total ROM Size (Code + RO Data + RW Data)      17496 (  17.09kB)

==============================================================================

