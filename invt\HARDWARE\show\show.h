#ifndef __SHOW_H
#define __SHOW_H	

#include "sys.h" 
#include "oled.h"
#include "pfc.h"
#include "key.h"
#include "inveter.h"

void show_memu(void);
void show_AD(void);
void show_pfc(pfc_typedef *pfc);
void show_udc(pfc_typedef *pfc);
void pf_adjust(pfc_typedef *pfc);
void vref_adjust(inveter_typedef *invt);
void show2(inveter_typedef *invt);

void freq_adjust(void);
void update_TIM3_freq(void);
void ARR_adjust(inveter_typedef *invt);

extern const uint16_t ARR_Table[];

#endif



