--cpu=Cortex-M4.fp
"..\obj\main.o"
"..\obj\stm32f4xx_it.o"
"..\obj\system_stm32f4xx.o"
"..\obj\led.o"
"..\obj\sine.o"
"..\obj\vofa.o"
"..\obj\ad.o"
"..\obj\oled.o"
"..\obj\pfc.o"
"..\obj\timer1.o"
"..\obj\show.o"
"..\obj\key.o"
"..\obj\inveter.o"
"..\obj\delay.o"
"..\obj\sys.o"
"..\obj\usart.o"
"..\obj\startup_stm32f40_41xxx.o"
"..\obj\misc.o"
"..\obj\stm32f4xx_gpio.o"
"..\obj\stm32f4xx_rcc.o"
"..\obj\stm32f4xx_syscfg.o"
"..\obj\stm32f4xx_usart.o"
"..\obj\stm32f4xx_adc.o"
"..\obj\stm32f4xx_dma.o"
"..\obj\stm32f4xx_tim.o"
"..\obj\stm32f4xx_dma2d.o"
"..\DSP_LIB\arm_cortexM4lf_math.lib"
--strict --scatter "..\OBJ\LED.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list "..\OBJ\LED.map" -o ..\OBJ\LED.axf