#include "AD.h"


uint16_t AD_Value[hang][lie];					//定义用于存放AD转换结果的全局数组
uint16_t ADC_Value[size];					//定义用于存放AD转换结果的全局数组


void MyGPIO_Init(void)
{	 
	 GPIO_InitTypeDef  GPIO_InitStructure;
	
	 RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOA, ENABLE);	 
     //先初始化ADC1通道5 IO口
	 GPIO_InitStructure.GPIO_Pin = GPIO_Pin_2;				 //PA5 通道5
	 GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AN;			 //模拟输入
	 GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_NOPULL ;		 //不带上下拉
	 GPIO_Init(GPIOA, &GPIO_InitStructure);					 //初始化  
}


void MyADC1_Init(void)
{
	 GPIO_InitTypeDef  GPIO_InitStructure;
	 ADC_InitTypeDef       	ADC_InitStructure;
	 ADC_CommonInitTypeDef 	ADC_CommonInitStructure;

	/*==================1.开启ADC1RCC时钟===============*/
	 RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOA, ENABLE);	
	 RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOC, ENABLE);	
	 RCC_APB2PeriphResetCmd(RCC_APB2Periph_ADC1,ENABLE);	 //ADC1复位
	 RCC_APB2PeriphResetCmd(RCC_APB2Periph_ADC1,DISABLE);	 //复位结束	 
  
	
	 GPIO_InitStructure.GPIO_Pin = GPIO_Pin_2 | GPIO_Pin_1 | GPIO_Pin_5 | GPIO_Pin_3 | GPIO_Pin_4 | GPIO_Pin_0;				 //PA5 通道5
	 GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AN;			 //模拟输入
	 GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_NOPULL ;		 //不带上下拉
	 GPIO_Init(GPIOA, &GPIO_InitStructure);					 //初始化  

	 GPIO_InitStructure.GPIO_Pin = GPIO_Pin_0 | GPIO_Pin_2 | GPIO_Pin_1 | GPIO_Pin_3;				 //PA5 通道5
	 GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AN;			 //模拟输入
	 GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_NOPULL ;		 //不带上下拉
	 GPIO_Init(GPIOC, &GPIO_InitStructure);					 //初始化  

	 //ADC初始化配置

	 RCC_APB2PeriphClockCmd(RCC_APB2Periph_ADC1, ENABLE); 	 
	 ADC_CommonInitStructure.ADC_Mode = ADC_Mode_Independent;						 //独立模式
	 ADC_CommonInitStructure.ADC_TwoSamplingDelay = ADC_TwoSamplingDelay_15Cycles;	 //两个采样阶段之间的延迟5个时钟
	 ADC_CommonInitStructure.ADC_DMAAccessMode = ADC_DMAAccessMode_Disabled; 		 //DMA失能
	 ADC_CommonInitStructure.ADC_Prescaler = ADC_Prescaler_Div4;				     //预分频4分频。ADCCLK=PCLK2/4=84/4=21Mhz,ADC时钟最好不要超过36Mhz 
	 ADC_CommonInit(&ADC_CommonInitStructure);		 		 //初始化
	 
	 ADC_InitStructure.ADC_Resolution = ADC_Resolution_12b;      					 //12位模式
	 ADC_InitStructure.ADC_ScanConvMode = ENABLE;			     					 //非扫描模式	
	 ADC_InitStructure.ADC_ContinuousConvMode = DISABLE;		    				     //连续转换
	 ADC_InitStructure.ADC_ExternalTrigConvEdge = ADC_ExternalTrigConvEdge_Rising;     //禁止触发检测，使用软件触发
	 ADC_InitStructure.ADC_DataAlign = ADC_DataAlign_Right;	    				     //右对齐	
	 ADC_InitStructure.ADC_ExternalTrigConv = ADC_ExternalTrigConv_T3_TRGO;
//	 ADC_InitStructure.ADC_ExternalTrigConv = ADC_ExternalTrigConv_T3_CC1;
	 ADC_InitStructure.ADC_NbrOfConversion = size;					  				     //1个转换在规则序列中 也就是只转换规则序列1 
	 ADC_Init(ADC1, &ADC_InitStructure);						  				     //ADC初始化
	
 	 ADC_RegularChannelConfig(ADC1, ADC_Channel_10, 1, ADC_SampleTime_56Cycles );	//PC1	eab
 	 ADC_RegularChannelConfig(ADC1, ADC_Channel_1, 2, ADC_SampleTime_56Cycles );	//PA1	ebc
 	 ADC_RegularChannelConfig(ADC1, ADC_Channel_12, 3, ADC_SampleTime_56Cycles );	//PC2 ia
 	 ADC_RegularChannelConfig(ADC1, ADC_Channel_13, 4, ADC_SampleTime_56Cycles );	//PC3 ic
//	 ADC_ExternalTrigConvCmd(ADC1,ENABLE);              //启用外部触发ADC1转换
	
	 ADC_Cmd(ADC1,ENABLE);							   //启用ADC1
	
//	ADC_ResetCalibration(ADC1);						   //ADC1复位校准
//	while(ADC_GetResetCalibrationStatus(ADC1));		   //等待复位校准完成
//	ADC_StartCalibration(ADC1);						   //ADC1 开始校准
//	while(ADC_GetCalibrationStatus(ADC1));             //等待ADC校准完成
}


void MyTIM3_Init(uint16_t arr,uint16_t psc)
{
	
//	GPIO_InitTypeDef GPIO_InitStructure;
//	TIM_OCInitTypeDef TIM_OCInitStruct;
	TIM_TimeBaseInitTypeDef TIM_TimeBaseInitStruct;
	RCC_APB1PeriphClockCmd(RCC_APB1Periph_TIM3,ENABLE);
	
//	GPIO_PinAFConfig(GPIOA,GPIO_PinSource6,GPIO_AF_TIM3); //GPIOF9复用为定时器14
//	RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOA, ENABLE); 	//使能PORTF时钟	



//	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_6;           //GPIOF9
//	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF;        //复用功能
//	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_100MHz;	//速度100MHz
//	GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;      //推挽复用输出
//	GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_UP;        //上拉
//	GPIO_Init(GPIOA,&GPIO_InitStructure);              //初始化PF9

	/*==============1.初始化TIM2=============*/
	TIM_TimeBaseInitStruct.TIM_ClockDivision = TIM_CKD_DIV1;
	TIM_TimeBaseInitStruct.TIM_CounterMode = TIM_CounterMode_Up;
    TIM_TimeBaseInitStruct.TIM_RepetitionCounter = 0;			//只有高级定时器需要设置，其他定时器可以不设置。
	TIM_TimeBaseInitStruct.TIM_Period = arr - 1;
	TIM_TimeBaseInitStruct.TIM_Prescaler = psc - 1;
	TIM_TimeBaseInit(TIM3,&TIM_TimeBaseInitStruct);
	
	/*==============2.配置TIM2PWM模式=============*/
//	TIM_OCInitStruct.TIM_OCMode = TIM_OCMode_PWM1;				//设置输出模式：PWM模式
//	TIM_OCInitStruct.TIM_OutputState = TIM_OutputState_Enable;	//设置比较输出使能：
//	TIM_OCInitStruct.TIM_OCPolarity = TIM_OCPolarity_High;		//设置ref为有效电平时 输出高电平
//	TIM_OCInitStruct.TIM_Pulse = 10;							//输出占空比：50%的PWM波
//	TIM_OC1Init(TIM3,&TIM_OCInitStruct);
//	TIM_OC1PreloadConfig(TIM3, TIM_OCPreload_Enable);  //使能TIM14在CCR1上的预装载寄存器
// 
//  TIM_ARRPreloadConfig(TIM3,ENABLE);//ARPE使能 
//	TIM_CtrlPWMOutputs(TIM3,ENABLE);							//PWM输出使能
//	TIM_SelectOutputTrigger(TIM3, TIM_TRGOSource_OC1Ref);

	TIM_SelectOutputTrigger(TIM3, TIM_TRGOSource_Update);
	TIM_Cmd(TIM3,DISABLE);										//ADC1失能	初始化全部完成在开启定时器
}


void MyDMA1_Init(void)
{
	 DMA_InitTypeDef DMA_InitStructure;
   	 NVIC_InitTypeDef NVIC_InitStruct;

	 RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_DMA2, ENABLE);
	

	 RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_DMA2, ENABLE);
	 DMA_InitStructure.DMA_Channel = DMA_Channel_0;  					   	    //通道选择
	 DMA_InitStructure.DMA_PeripheralBaseAddr = (uint32_t)&ADC1->DR;			//DMA外设地址
	 DMA_InitStructure.DMA_Memory0BaseAddr = (uint32_t)&ADC_Value;			    //DMA存储器0地址
	 DMA_InitStructure.DMA_DIR = DMA_DIR_PeripheralToMemory;			  	    //数据传输方向 外设到存储器
	 DMA_InitStructure.DMA_BufferSize = size;			 	    //数据传输量 
	 DMA_InitStructure.DMA_PeripheralInc = DMA_PeripheralInc_Disable;	    	//失能外设地址自增
	 DMA_InitStructure.DMA_MemoryInc = DMA_MemoryInc_Enable;			    	//使能存储器地址自增
	 DMA_InitStructure.DMA_PeripheralDataSize = DMA_PeripheralDataSize_HalfWord;		//外设数据长度:8位
	 DMA_InitStructure.DMA_MemoryDataSize = DMA_MemoryDataSize_HalfWord;			    //存储器数据长度:8位
  	 DMA_InitStructure.DMA_Mode = DMA_Mode_Circular;							// 使用循环模式 
	 DMA_InitStructure.DMA_Priority = DMA_Priority_High;						//高优先级
	 //FIFO配置
	 DMA_InitStructure.DMA_FIFOMode = DMA_FIFOMode_Disable;         			//失能FIFO
	 DMA_InitStructure.DMA_FIFOThreshold = DMA_FIFOThreshold_Full;			
	 DMA_InitStructure.DMA_MemoryBurst = DMA_MemoryBurst_Single;				//存储器突发单次传输
	 DMA_InitStructure.DMA_PeripheralBurst = DMA_PeripheralBurst_Single;		//外设突发单次传输
	 DMA_Init(DMA2_Stream0, &DMA_InitStructure);								//初始化DMA Stream
	
	/*===============使能DMA中断==============*/
	NVIC_InitStruct.NVIC_IRQChannel = DMA2_Stream0_IRQn;
	NVIC_InitStruct.NVIC_IRQChannelCmd = ENABLE;
	NVIC_InitStruct.NVIC_IRQChannelPreemptionPriority =1;
	NVIC_InitStruct.NVIC_IRQChannelSubPriority = 0;
	NVIC_Init(&NVIC_InitStruct);
	NVIC_PriorityGroupConfig(NVIC_PriorityGroup_1);
	
	DMA_ITConfig (DMA2_Stream0,DMA_IT_TC,ENABLE);//使能DMA1中断
	
	/*==================使能ADC1DMA发送===================*/
	ADC_DMARequestAfterLastTransferCmd(ADC1, ENABLE);
	ADC_DMACmd(ADC1,ENABLE);
	
	/*==================使能DMA1通道1 开启传输===================*/
	DMA_Cmd(DMA2_Stream0,ENABLE);
}
