..\obj\pfc.o: ..\HARDWARE\pfc\pfc.c
..\obj\pfc.o: ..\HARDWARE\pfc\pfc.h
..\obj\pfc.o: ..\SYSTEM\sys\sys.h
..\obj\pfc.o: ..\USER\stm32f4xx.h
..\obj\pfc.o: ..\CORE\core_cm4.h
..\obj\pfc.o: D:\software\keil5arm\ARM\ARMCC\Bin\..\include\stdint.h
..\obj\pfc.o: ..\CORE\core_cmInstr.h
..\obj\pfc.o: ..\CORE\core_cmFunc.h
..\obj\pfc.o: ..\CORE\core_cm4_simd.h
..\obj\pfc.o: ..\USER\system_stm32f4xx.h
..\obj\pfc.o: ..\USER\stm32f4xx_conf.h
..\obj\pfc.o: ..\FWLIB\inc\stm32f4xx_adc.h
..\obj\pfc.o: ..\USER\stm32f4xx.h
..\obj\pfc.o: ..\FWLIB\inc\stm32f4xx_crc.h
..\obj\pfc.o: ..\FWLIB\inc\stm32f4xx_dbgmcu.h
..\obj\pfc.o: ..\FWLIB\inc\stm32f4xx_dma.h
..\obj\pfc.o: ..\FWLIB\inc\stm32f4xx_exti.h
..\obj\pfc.o: ..\FWLIB\inc\stm32f4xx_flash.h
..\obj\pfc.o: ..\FWLIB\inc\stm32f4xx_gpio.h
..\obj\pfc.o: ..\FWLIB\inc\stm32f4xx_i2c.h
..\obj\pfc.o: ..\FWLIB\inc\stm32f4xx_iwdg.h
..\obj\pfc.o: ..\FWLIB\inc\stm32f4xx_pwr.h
..\obj\pfc.o: ..\FWLIB\inc\stm32f4xx_rcc.h
..\obj\pfc.o: ..\FWLIB\inc\stm32f4xx_rtc.h
..\obj\pfc.o: ..\FWLIB\inc\stm32f4xx_sdio.h
..\obj\pfc.o: ..\FWLIB\inc\stm32f4xx_spi.h
..\obj\pfc.o: ..\FWLIB\inc\stm32f4xx_syscfg.h
..\obj\pfc.o: ..\FWLIB\inc\stm32f4xx_tim.h
..\obj\pfc.o: ..\FWLIB\inc\stm32f4xx_usart.h
..\obj\pfc.o: ..\FWLIB\inc\stm32f4xx_wwdg.h
..\obj\pfc.o: ..\FWLIB\inc\misc.h
..\obj\pfc.o: ..\FWLIB\inc\stm32f4xx_cryp.h
..\obj\pfc.o: ..\FWLIB\inc\stm32f4xx_hash.h
..\obj\pfc.o: ..\FWLIB\inc\stm32f4xx_rng.h
..\obj\pfc.o: ..\FWLIB\inc\stm32f4xx_can.h
..\obj\pfc.o: ..\FWLIB\inc\stm32f4xx_dac.h
..\obj\pfc.o: ..\FWLIB\inc\stm32f4xx_dcmi.h
..\obj\pfc.o: ..\FWLIB\inc\stm32f4xx_fsmc.h
..\obj\pfc.o: ..\HARDWARE\sine\sine.h
..\obj\pfc.o: D:\software\keil5arm\ARM\ARMCC\Bin\..\include\math.h
..\obj\pfc.o: ..\DSP_LIB\Include\arm_math.h
..\obj\pfc.o: ..\DSP_LIB\Include\core_cm4.h
..\obj\pfc.o: D:\software\keil5arm\ARM\ARMCC\Bin\..\include\string.h
